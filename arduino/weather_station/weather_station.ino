/*
 * NxtAcre Weather Station
 * 
 * This sketch implements a weather station using an ESP32 board with WiFi capabilities
 * for the NxtAcre Farm Management Platform. It collects data from various sensors
 * (temperature, humidity, pressure, rainfall, wind speed, wind direction) and sends
 * the data to the NxtAcre server via WiFi.
 * 
 * Hardware:
 * - ESP32 development board
 * - BME280 sensor (temperature, humidity, pressure)
 * - Rain gauge sensor
 * - Anemometer (wind speed sensor)
 * - Wind vane (wind direction sensor)
 * - Solar panel and battery for power
 * 
 * Dependencies:
 * - WiFi.h
 * - HTTPClient.h
 * - ArduinoJson.h
 * - Adafruit_BME280.h
 * 
 * Created for NxtAcre Farm Management Platform
 */

#include "config.h"
#include "wifi_handler.h"
#include "sensor_handler.h"

// Global variables
unsigned long lastDataSendTime = 0;
unsigned long lastSensorReadTime = 0;
WeatherData weatherData;

void setup() {
  // Initialize serial communication
  Serial.begin(115200);
  Serial.println("\n\nNxtAcre Weather Station");
  Serial.println("------------------------");
  
  // Initialize sensors
  if (!initializeSensors()) {
    Serial.println("Failed to initialize one or more sensors. Check connections.");
    // Continue anyway, as some sensors might still work
  }
  
  // Initialize WiFi
  initializeWiFi();
}

void loop() {
  // Handle WiFi connection
  handleWiFi();
  
  // Read sensors at the specified interval
  unsigned long currentMillis = millis();
  if (currentMillis - lastSensorReadTime >= SENSOR_READ_INTERVAL) {
    lastSensorReadTime = currentMillis;
    readSensors(&weatherData);
    
    // Print sensor data to serial for debugging
    printWeatherData(&weatherData);
  }
  
  // Send data to server at the specified interval
  if (currentMillis - lastDataSendTime >= DATA_SEND_INTERVAL && WiFi.status() == WL_CONNECTED) {
    lastDataSendTime = currentMillis;
    sendDataToServer(&weatherData);
  }
  
  // Small delay to prevent CPU hogging
  delay(100);
}

void printWeatherData(WeatherData* data) {
  Serial.println("\n--- Weather Data ---");
  Serial.print("Temperature: "); Serial.print(data->temperature); Serial.println(" °C");
  Serial.print("Humidity: "); Serial.print(data->humidity); Serial.println(" %");
  Serial.print("Pressure: "); Serial.print(data->pressure); Serial.println(" hPa");
  Serial.print("Rainfall: "); Serial.print(data->rainfall); Serial.println(" mm");
  Serial.print("Wind Speed: "); Serial.print(data->windSpeed); Serial.println(" m/s");
  Serial.print("Wind Direction: "); Serial.print(data->windDirection); Serial.println(" degrees");
  Serial.print("Battery Level: "); Serial.print(data->batteryLevel); Serial.println(" %");
  Serial.print("Solar Panel Voltage: "); Serial.print(data->solarVoltage); Serial.println(" V");
  Serial.println("-------------------");
}