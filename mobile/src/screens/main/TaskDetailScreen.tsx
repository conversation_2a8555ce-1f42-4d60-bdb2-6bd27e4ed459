import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  TextInput,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainStackParamList } from '@/navigation/MainNavigator';
import { taskService, Task, Comment } from '@/services/taskService';
import { useAuth } from '@/store/AuthProvider';

type TaskDetailScreenProps = NativeStackScreenProps<MainStackParamList, 'TaskDetail'>;

const TaskDetailScreen: React.FC<TaskDetailScreenProps> = ({ route, navigation }) => {
  const { taskId } = route.params;
  const [task, setTask] = useState<Task | null>(null);
  const [loading, setLoading] = useState(true);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [editedTask, setEditedTask] = useState<Partial<Task>>({});
  const [newComment, setNewComment] = useState('');
  const [isStatusModalVisible, setIsStatusModalVisible] = useState(false);

  const { user } = useAuth();

  useEffect(() => {
    const fetchTaskData = async () => {
      try {
        setLoading(true);

        // Fetch task details with comments
        const taskDetails = await taskService.getTaskById(taskId);

        if (taskDetails) {
          setTask(taskDetails);
          setEditedTask(taskDetails);
        } else {
          // If task details couldn't be fetched, try to get individual data
          console.warn(`Could not fetch complete task details for task ${taskId}, trying individual endpoints`);

          // Try to get task data
          const taskData = await taskService.getTaskById(taskId);
          if (taskData) {
            setTask(taskData);
            setEditedTask(taskData);
          } else {
            console.error(`Could not fetch task data for task ${taskId}`);
            Alert.alert('Error', 'Could not load task data. Please try again later.');
          }

          // Try to get comments
          if (taskData) {
            const commentsData = await taskService.getTaskComments(taskId);
            setTask(prev => prev ? { ...prev, comments: commentsData } : null);
          }
        }
      } catch (error) {
        console.error('Error fetching task data:', error);
        Alert.alert('Error', 'Could not load task data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (taskId) {
      fetchTaskData();
    }
  }, [taskId]);

  const handleUpdateTask = async () => {
    if (!editedTask.title?.trim()) {
      Alert.alert('Error', 'Task title is required');
      return;
    }

    if (!task?.id) {
      Alert.alert('Error', 'Task ID is missing');
      return;
    }

    setLoading(true);

    try {
      const updatedTask = await taskService.updateTask(task.id, editedTask);

      if (updatedTask) {
        setTask(updatedTask);
        setIsEditModalVisible(false);
        Alert.alert('Success', 'Task updated successfully');
      } else {
        Alert.alert('Error', 'Failed to update task. Please try again.');
      }
    } catch (error) {
      console.error('Error updating task:', error);
      Alert.alert('Error', 'Failed to update task. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateStatus = async (newStatus: Task['status']) => {
    if (!task?.id) {
      Alert.alert('Error', 'Task ID is missing');
      return;
    }

    setLoading(true);

    try {
      const updatedTask = await taskService.updateTaskStatus(task.id, newStatus);

      if (updatedTask) {
        setTask(updatedTask);
        setIsStatusModalVisible(false);
      } else {
        Alert.alert('Error', 'Failed to update task status. Please try again.');
      }
    } catch (error) {
      console.error('Error updating task status:', error);
      Alert.alert('Error', 'Failed to update task status. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAddComment = async () => {
    if (!newComment.trim()) {
      return;
    }

    if (!task?.id) {
      Alert.alert('Error', 'Task ID is missing');
      return;
    }

    if (!user?.name) {
      Alert.alert('Error', 'User information is missing');
      return;
    }

    setLoading(true);

    try {
      const commentData = {
        text: newComment,
        createdBy: user.name,
      };

      const newCommentObj = await taskService.addTaskComment(task.id, commentData);

      if (newCommentObj) {
        setTask({
          ...task,
          comments: [...(task.comments || []), newCommentObj],
        });
        setNewComment('');
      } else {
        Alert.alert('Error', 'Failed to add comment. Please try again.');
      }
    } catch (error) {
      console.error('Error adding comment:', error);
      Alert.alert('Error', 'Failed to add comment. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low':
        return '#9ca3af';
      case 'medium':
        return '#60a5fa';
      case 'high':
        return '#f59e0b';
      case 'urgent':
        return '#ef4444';
      default:
        return '#9ca3af';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return '#f59e0b';
      case 'in-progress':
        return '#60a5fa';
      case 'completed':
        return '#22c55e';
      case 'cancelled':
        return '#9ca3af';
      default:
        return '#9ca3af';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return 'time-outline';
      case 'in-progress':
        return 'play-outline';
      case 'completed':
        return 'checkmark-circle-outline';
      case 'cancelled':
        return 'close-circle-outline';
      default:
        return 'help-circle-outline';
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
        <Text style={styles.loadingText}>Loading task details...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <Text style={styles.title}>{task?.title}</Text>
            <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(task?.priority || 'medium') }]}>
              <Text style={styles.priorityText}>{task?.priority.toUpperCase()}</Text>
            </View>
          </View>

          <View style={styles.headerButtons}>
            <TouchableOpacity 
              style={styles.timeTrackButton} 
              onPress={() => navigation.navigate('TimeEntry', { taskId: task?.id })}
            >
              <Ionicons name="time-outline" size={20} color="#22c55e" />
              <Text style={styles.timeTrackText}>Track Time</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.editButton} onPress={() => setIsEditModalVisible(true)}>
              <Ionicons name="create-outline" size={24} color="#22c55e" />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.statusSection}>
          <View style={styles.statusHeader}>
            <Text style={styles.sectionTitle}>Status</Text>
            <TouchableOpacity onPress={() => setIsStatusModalVisible(true)}>
              <Text style={styles.changeStatusText}>Change Status</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.statusContainer}>
            <Ionicons
              name={getStatusIcon(task?.status || 'pending')}
              size={24}
              color={getStatusColor(task?.status || 'pending')}
            />
            <Text style={[styles.statusText, { color: getStatusColor(task?.status || 'pending') }]}>
              {task?.status.toUpperCase()}
            </Text>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Details</Text>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Due Date:</Text>
            <Text style={styles.detailValue}>{task?.dueDate}</Text>
          </View>

          {task?.assignedTo && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Assigned To:</Text>
              <Text style={styles.detailValue}>{task.assignedTo}</Text>
            </View>
          )}

          {task?.fieldName && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Field:</Text>
              <TouchableOpacity 
                onPress={() => navigation.navigate('FieldDetail', { fieldId: task.fieldId! })}
              >
                <Text style={styles.linkValue}>{task.fieldName}</Text>
              </TouchableOpacity>
            </View>
          )}

          {task?.equipmentName && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Equipment:</Text>
              <Text style={styles.detailValue}>{task.equipmentName}</Text>
            </View>
          )}

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Created:</Text>
            <Text style={styles.detailValue}>{task?.createdAt}</Text>
          </View>

          {task?.createdBy && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Created By:</Text>
              <Text style={styles.detailValue}>{task.createdBy}</Text>
            </View>
          )}
        </View>

        {task?.description && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Description</Text>
            <Text style={styles.description}>{task.description}</Text>
          </View>
        )}

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Comments</Text>

          {task?.comments && task.comments.length > 0 ? (
            task.comments.map(comment => (
              <View key={comment.id} style={styles.commentItem}>
                <View style={styles.commentHeader}>
                  <Text style={styles.commentAuthor}>{comment.createdBy}</Text>
                  <Text style={styles.commentDate}>{comment.createdAt}</Text>
                </View>
                <Text style={styles.commentText}>{comment.text}</Text>
              </View>
            ))
          ) : (
            <Text style={styles.noCommentsText}>No comments yet</Text>
          )}

          <View style={styles.addCommentContainer}>
            <TextInput
              style={styles.commentInput}
              placeholder="Add a comment..."
              value={newComment}
              onChangeText={setNewComment}
              multiline
            />
            <TouchableOpacity 
              style={[styles.addCommentButton, !newComment.trim() && styles.disabledButton]}
              onPress={handleAddComment}
              disabled={!newComment.trim()}
            >
              <Ionicons name="send" size={20} color={newComment.trim() ? "#fff" : "#a1a1aa"} />
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Edit Task Modal */}
      <Modal
        visible={isEditModalVisible}
        transparent
        animationType="slide"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Edit Task</Text>

            <Text style={styles.inputLabel}>Title *</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter task title"
              value={editedTask.title}
              onChangeText={(text) => setEditedTask({ ...editedTask, title: text })}
            />

            <Text style={styles.inputLabel}>Description</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Enter task description"
              value={editedTask.description}
              onChangeText={(text) => setEditedTask({ ...editedTask, description: text })}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />

            <Text style={styles.inputLabel}>Priority</Text>
            <View style={styles.prioritySelector}>
              {['low', 'medium', 'high', 'urgent'].map(priority => (
                <TouchableOpacity
                  key={priority}
                  style={[
                    styles.priorityOption,
                    editedTask.priority === priority && { backgroundColor: getPriorityColor(priority) },
                  ]}
                  onPress={() => setEditedTask({ ...editedTask, priority: priority as any })}
                >
                  <Text
                    style={[
                      styles.priorityOptionText,
                      editedTask.priority === priority && { color: '#fff' },
                    ]}
                  >
                    {priority}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <Text style={styles.inputLabel}>Due Date</Text>
            <TextInput
              style={styles.input}
              placeholder="YYYY-MM-DD"
              value={editedTask.dueDate}
              onChangeText={(text) => setEditedTask({ ...editedTask, dueDate: text })}
            />

            <Text style={styles.inputLabel}>Assign To</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter assignee name"
              value={editedTask.assignedTo}
              onChangeText={(text) => setEditedTask({ ...editedTask, assignedTo: text })}
            />

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setIsEditModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.saveButton]}
                onPress={handleUpdateTask}
              >
                <Text style={styles.saveButtonText}>Save Changes</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Status Change Modal */}
      <Modal
        visible={isStatusModalVisible}
        transparent
        animationType="slide"
      >
        <View style={styles.modalContainer}>
          <View style={styles.statusModalContent}>
            <Text style={styles.modalTitle}>Update Status</Text>

            {['pending', 'in-progress', 'completed', 'cancelled'].map(status => (
              <TouchableOpacity
                key={status}
                style={[
                  styles.statusOption,
                  task?.status === status && styles.selectedStatusOption,
                  { borderColor: getStatusColor(status) }
                ]}
                onPress={() => handleUpdateStatus(status as any)}
              >
                <Ionicons
                  name={getStatusIcon(status)}
                  size={24}
                  color={getStatusColor(status)}
                  style={styles.statusOptionIcon}
                />
                <Text
                  style={[
                    styles.statusOptionText,
                    { color: getStatusColor(status) }
                  ]}
                >
                  {status.toUpperCase()}
                </Text>
              </TouchableOpacity>
            ))}

            <TouchableOpacity
              style={[styles.modalButton, styles.cancelButton, styles.closeButton]}
              onPress={() => setIsStatusModalVisible(false)}
            >
              <Text style={styles.cancelButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    backgroundColor: '#fff',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  titleContainer: {
    flex: 1,
    marginRight: 10,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  priorityBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginTop: 5,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeTrackButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 10,
  },
  timeTrackText: {
    color: '#22c55e',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  editButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusSection: {
    backgroundColor: '#fff',
    padding: 15,
    marginBottom: 10,
  },
  statusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  changeStatusText: {
    fontSize: 14,
    color: '#22c55e',
    fontWeight: '500',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  section: {
    backgroundColor: '#fff',
    padding: 15,
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  detailLabel: {
    width: 100,
    fontSize: 14,
    color: '#666',
  },
  detailValue: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  linkValue: {
    flex: 1,
    fontSize: 14,
    color: '#22c55e',
  },
  description: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
  },
  commentItem: {
    marginBottom: 15,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  commentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  commentAuthor: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  commentDate: {
    fontSize: 12,
    color: '#666',
  },
  commentText: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
  },
  noCommentsText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    marginBottom: 15,
  },
  addCommentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  commentInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 8,
    fontSize: 14,
    maxHeight: 100,
  },
  addCommentButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#22c55e',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 10,
  },
  disabledButton: {
    backgroundColor: '#e5e7eb',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    width: '90%',
    maxHeight: '90%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  statusModalContent: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    width: '90%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 5,
    color: '#666',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 15,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  prioritySelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  priorityOption: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 4,
    borderRadius: 8,
    backgroundColor: '#f3f4f6',
    marginHorizontal: 4,
    alignItems: 'center',
  },
  priorityOptionText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#666',
  },
  statusOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 10,
  },
  selectedStatusOption: {
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
  },
  statusOptionIcon: {
    marginRight: 10,
  },
  statusOptionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 8,
  },
  closeButton: {
    marginTop: 10,
  },
  cancelButton: {
    backgroundColor: '#f3f4f6',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666',
  },
  saveButton: {
    backgroundColor: '#22c55e',
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#fff',
  },
});

export default TaskDetailScreen;
