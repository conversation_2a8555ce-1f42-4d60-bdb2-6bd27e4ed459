import axios from 'axios';
import databaseService from './databaseService';
import networkService from './networkService';
import api from './apiClient';

/**
 * Synchronization service for handling offline data synchronization
 * This service provides methods for synchronizing local data with the server when connectivity is restored
 */

// Flag to track if synchronization is currently in progress
let isSynchronizing = false;

// Initialize the synchronization service
const initialize = async (): Promise<void> => {
  // Start monitoring network status
  networkService.startNetworkMonitoring();
  
  // Add listener for network status changes
  networkService.addNetworkStatusListener(async (status) => {
    // If the device is now connected to the internet and not already synchronizing
    if (status.isConnected && !isSynchronizing) {
      // Start synchronization process
      await synchronize();
    }
  });
  
  // Check if we're online and can synchronize immediately
  const isOnline = await networkService.isConnected();
  if (isOnline && !isSynchronizing) {
    await synchronize();
  }
};

// Main synchronization function
const synchronize = async (): Promise<void> => {
  // Set flag to prevent multiple synchronization processes
  isSynchronizing = true;
  
  try {
    console.log('Starting data synchronization...');
    
    // Process sync queue
    await processSyncQueue();
    
    // Sync GPS recordings
    await syncGpsRecordings();
    
    console.log('Data synchronization completed successfully');
  } catch (error) {
    console.error('Error during data synchronization:', error);
  } finally {
    // Reset flag
    isSynchronizing = false;
  }
};

// Process the sync queue
const processSyncQueue = async (): Promise<void> => {
  try {
    // Get all items from the sync queue
    const queueItems = await databaseService.getSyncQueue();
    
    if (queueItems.length === 0) {
      console.log('Sync queue is empty');
      return;
    }
    
    console.log(`Processing ${queueItems.length} items in sync queue`);
    
    // Process each item in the queue
    for (const item of queueItems) {
      try {
        // Make the API request
        await axios({
          method: item.method,
          url: item.url,
          data: item.data,
          headers: item.headers,
        });
        
        // If successful, remove the item from the queue
        await databaseService.removeFromSyncQueue(item.id);
        console.log(`Successfully processed sync queue item: ${item.method} ${item.url}`);
      } catch (error) {
        console.error(`Error processing sync queue item: ${item.method} ${item.url}`, error);
        // We don't remove failed items from the queue so they can be retried later
      }
    }
  } catch (error) {
    console.error('Error processing sync queue:', error);
    throw error;
  }
};

// Sync GPS recordings that haven't been synced yet
const syncGpsRecordings = async (): Promise<void> => {
  try {
    // Get all unsynced GPS recordings
    const unsyncedRecordings = await databaseService.getUnsyncedGpsRecordings();
    
    if (unsyncedRecordings.length === 0) {
      console.log('No unsynced GPS recordings');
      return;
    }
    
    console.log(`Syncing ${unsyncedRecordings.length} GPS recordings`);
    
    // Sync each recording
    for (const recording of unsyncedRecordings) {
      try {
        // Send the recording to the server
        await api.post('/gps-recordings', recording);
        
        // Mark the recording as synced
        await databaseService.markGpsRecordingAsSynced(recording.id);
        console.log(`Successfully synced GPS recording: ${recording.id}`);
      } catch (error) {
        console.error(`Error syncing GPS recording: ${recording.id}`, error);
        // We don't update the synced status so it can be retried later
      }
    }
  } catch (error) {
    console.error('Error syncing GPS recordings:', error);
    throw error;
  }
};

// Force synchronization (can be called manually)
const forceSynchronize = async (): Promise<void> => {
  // Check if we're online
  const isOnline = await networkService.isConnected();
  
  if (!isOnline) {
    console.log('Cannot synchronize: device is offline');
    return;
  }
  
  if (isSynchronizing) {
    console.log('Synchronization already in progress');
    return;
  }
  
  // Start synchronization
  await synchronize();
};

// Add an API request to the sync queue (for offline operations)
const queueApiRequest = async (
  method: string,
  url: string,
  data?: any,
  headers?: any
): Promise<void> => {
  await databaseService.addToSyncQueue(method, url, data, headers);
  console.log(`Added API request to sync queue: ${method} ${url}`);
};

// Helper function to make API requests with offline support
const makeRequest = async <T>(
  method: string,
  url: string,
  data?: any,
  config?: any
): Promise<T> => {
  try {
    // Check if we're online
    const isOnline = await networkService.isConnected();
    
    if (!isOnline) {
      // If offline, add to sync queue and throw error
      await queueApiRequest(method, url, data, config?.headers);
      throw new Error('Device is offline, request queued for later');
    }
    
    // If online, make the request directly
    let response;
    switch (method.toLowerCase()) {
      case 'get':
        response = await api.get<T>(url, config);
        break;
      case 'post':
        response = await api.post<T>(url, data, config);
        break;
      case 'put':
        response = await api.put<T>(url, data, config);
        break;
      case 'patch':
        response = await api.patch<T>(url, data, config);
        break;
      case 'delete':
        response = await api.delete<T>(url, config);
        break;
      default:
        throw new Error(`Unsupported method: ${method}`);
    }
    
    return response.data;
  } catch (error) {
    console.error(`Error making API request: ${method} ${url}`, error);
    throw error;
  }
};

// Export the synchronization service
const synchronizationService = {
  initialize,
  synchronize,
  forceSynchronize,
  queueApiRequest,
  makeRequest,
};

export default synchronizationService;