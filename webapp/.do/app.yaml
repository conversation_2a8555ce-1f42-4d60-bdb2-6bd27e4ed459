alerts:
  - rule: DEPLOYMENT_FAILED
  - rule: DOMAIN_FAILED
databases:
  - cluster_name: db-postgresql-nyc3-15185
    db_name: nxtacre
    db_user: doadmin
    engine: PG
    name: db-postgresql-nyc3-15185
    production: true
    version: "17"
domains:
  - domain: nxtacre.com
    type: PRIMARY
    wildcard: true
ingress:
  rules:
    - component:
        name: web
      match:
        path:
          prefix: /
name: nxtacre
region: nyc
services:
  - dockerfile_path: webapp/Dockerfile
    envs:
      - key: NODE_ENV
        scope: RUN_AND_BUILD_TIME
        value: "production"
      - key: PORT
        scope: RUN_AND_BUILD_TIME
        value: "3002"
      - key: DB_HOST
        scope: RUN_AND_BUILD_TIME
        value: "db-postgresql-nyc3-15185-do-user-21715522-0.f.db.ondigitalocean.com"
      - key: DB_PORT
        scope: RUN_AND_BUILD_TIME
        value: "25060"
      - key: DB_NAME
        scope: RUN_AND_BUILD_TIME
        value: "nxtacre"
      - key: DB_USER
        scope: RUN_AND_BUILD_TIME
        value: "doadmin"
      - key: DB_PASSWORD
        scope: RUN_AND_BUILD_TIME
        value: "AVNS_1A15p7ZlMpxQDk6GpEb"
      - key: DB_SCHEMA
        scope: RUN_AND_BUILD_TIME
        value: "site"
      - key: JWT_SECRET
        scope: RUN_AND_BUILD_TIME
        value: "0bc2a321e6201b64b94ccd9bc84f1efc297b9859c8237ad753a6070ec9ff4742a0b7e3a9790e09f771e0ba32fdc04e436f043500920dfb95ffef506baebb83380e740342a88f96d8505993aac0501a1ab1abdb967a2ceeea2db82fe941cbe6850cdac41052e4feb8b42424ebdd22e76bc68ebbc2f2bdd3915f577e69f6e01f7d48a1bf2154ebb68fb7191d585f0117931827d59dfa05620b34394420d17d06b427dcf596a6b173f76be4bd731872773e0d8b0b522ee84b69e61f8a952938ebcfa5dd74d7dd973eb041d0dc62e9483a0279854a708978e0164de6df46345fa689b568b658289dc4ab085e0ecb215316dc458d4657972c55039bf69c1b627336d1"
      - key: JWT_EXPIRES_IN
        scope: RUN_AND_BUILD_TIME
        value: "1d"
      - key: JWT_REFRESH_SECRET
        scope: RUN_AND_BUILD_TIME
        value: "e7d81fb3a8c94f5b2d6e9c8a7f0b5d3e2c1a9f8b7d6e5c4a3b2d1e0f9c8b7a6d5e4f3c2b1a0"
      - key: JWT_REFRESH_EXPIRES_IN
        scope: RUN_AND_BUILD_TIME
        value: "7d"
      - key: EMAIL_HOST
        scope: RUN_AND_BUILD_TIME
        value: "smtp-relay.brevo.com"
      - key: EMAIL_PORT
        scope: RUN_AND_BUILD_TIME
        value: "587"
      - key: EMAIL_USER
        scope: RUN_AND_BUILD_TIME
        value: "<EMAIL>"
      - key: EMAIL_PASSWORD
        scope: RUN_AND_BUILD_TIME
        value: "TvmB9FcDX5LIG1df"
      - key: EMAIL_FROM
        scope: RUN_AND_BUILD_TIME
        value: "<EMAIL>"
      - key: QUICKBOOKS_CLIENT_ID
        scope: RUN_AND_BUILD_TIME
        value: "ABh8bhCtjQBRdYCDPsM8ulQqQ8K93uH0AQL2s3vQiFn2Q6T0vp"
      - key: QUICKBOOKS_CLIENT_SECRET
        scope: RUN_AND_BUILD_TIME
        value: "Ec9xMu3cGyktDQpRPN4LlZc50NQlWBYU685gHtDL"
      - key: QUICKBOOKS_REDIRECT_URI
        scope: RUN_AND_BUILD_TIME
        value: "https://app.nxtacre.com/quickbooks/callback"
      - key: VITE_MAIN_DOMAIN
        scope: RUN_AND_BUILD_TIME
        value: "nxtacre.com"
      - key: FRONTEND_URL
        scope: RUN_AND_BUILD_TIME
        value: "https://app.nxtacre.com"
      - key: API_URL
        scope: RUN_AND_BUILD_TIME
        value: "https://api.nxtacre.com"
      - key: VITE_GOOGLE_MAPS_API_KEY
        scope: RUN_AND_BUILD_TIME
        value: "AIzaSyAhcNcr9eb9-ghjFFIeo8FRz_iUOJnAUyM"
      - key: GOOGLE_CLIENT_ID
        scope: RUN_AND_BUILD_TIME
        value: "992446935488-125hrqrkn5ua6o7bsegifgql2ld9e14t.apps.googleusercontent.com"
      - key: GOOGLE_CLIENT_SECRET
        scope: RUN_AND_BUILD_TIME
        value: "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"
      - key: GOOGLE_REDIRECT_URI
        scope: RUN_AND_BUILD_TIME
        value: "https://app.nxtacre.com/external-storage-auth/google/callback"
      - key: DROPBOX_APP_KEY
        scope: RUN_AND_BUILD_TIME
        value: "2wqa3emx05dij79"
      - key: DROPBOX_APP_SECRET
        scope: RUN_AND_BUILD_TIME
        value: "9qbmrg7yh4eglcz"
      - key: DROPBOX_REDIRECT_URI
        scope: RUN_AND_BUILD_TIME
        value: "https://app.nxtacre.com/external-storage-auth/dropbox/callback"
      - key: CRON_SECRET_KEY
        scope: RUN_AND_BUILD_TIME
        value: "1I)YIz|(/jaW4XUs;KI[a]FaQdvv@'|oL`E?',pw/o>=8FZBG+)Q~znxTc8A9e9"
      - key: DATABASE_URL
        scope: RUN_TIME
        value: "${db-postgresql-nyc3-15185.DATABASE_URL}"
      - key: SPACES_ENDPOINT
        scope: RUN_AND_BUILD_TIME
        value: "https://nxtacre.nyc3.digitaloceanspaces.com"
      - key: SPACES_REGION
        scope: RUN_AND_BUILD_TIME
        value: "nyc3"
      - key: SPACES_NAME
        scope: RUN_AND_BUILD_TIME
        value: "nxtacre"
      - key: SPACES_KEY
        scope: RUN_AND_BUILD_TIME
        value: "DO801A4KUJNYYYNZVP8Z"
      - key: SPACES_SECRET
        scope: RUN_AND_BUILD_TIME
        value: "kKc7B74SvLeZD7i3Oi0C1E6v9yF1xBjYNiNsBaH+bO0"
      - key: SENTRY_AUTH_TOKEN
        scope: RUN_AND_BUILD_TIME
        value: "***********************************************************************"
      - key: SENTRY_ORG
        scope: RUN_AND_BUILD_TIME
        value: "peak-solutions-llc"
      - key: SENTRY_PROJECT
        scope: RUN_AND_BUILD_TIME
        value: "javascript-react"
      - key: VITE_API_URL
        scope: RUN_AND_BUILD_TIME
        value: "https://api.nxtacre.com"
      - key: ENCRYPTION_KEY
        scope: RUN_AND_BUILD_TIME
        value: ".pBww.UeXq]4EO|j`!slO`]yf.&a9[&*Csf"
      - key: OPENAI_API_KEY
        scope: RUN_AND_BUILD_TIME
        value: "********************************************************************************************************************************************************************"
      - key: OPENWEATHER_API_KEY
        scope: RUN_AND_BUILD_TIME
        value: "28359ffee528b744c3e1b0ecc6bf056f"
      - key: GRANTS_GOV_API_KEY
        scope: RUN_AND_BUILD_TIME
        value: "GPHYbyfrWRyG5Ao8LcqTBnV2p7SkopGotJylzXAo"
      - key: GRANTS_GOV_API_URL
        scope: RUN_AND_BUILD_TIME
        value: "https://www.grants.gov/grantsws/rest"
      - key: FARMERS_GOV_API_KEY
        scope: RUN_AND_BUILD_TIME
        value: "GPHYbyfrWRyG5Ao8LcqTBnV2p7SkopGotJylzXAo"
      - key: FARMERS_GOV_API_URL
        scope: RUN_AND_BUILD_TIME
        value: "https://www.farmers.gov/api"
      - key: USDA_ARMS_API_KEY
        scope: RUN_AND_BUILD_TIME
        value: "GPHYbyfrWRyG5Ao8LcqTBnV2p7SkopGotJylzXAo"
      - key: USDA_ARMS_API_URL
        scope: RUN_AND_BUILD_TIME
        value: "https://api.ers.usda.gov/arms"
      - key: FARM_SERVICE_AGENCY_API_KEY
        scope: RUN_AND_BUILD_TIME
        value: "GPHYbyfrWRyG5Ao8LcqTBnV2p7SkopGotJylzXAo"
      - key: FARM_SERVICE_AGENCY_API_URL
        scope: RUN_AND_BUILD_TIME
        value: "https://api.fsa.usda.gov"
      - key: RURAL_DEVELOPMENT_API_KEY
        scope: RUN_AND_BUILD_TIME
        value: "GPHYbyfrWRyG5Ao8LcqTBnV2p7SkopGotJylzXAo"
      - key: RURAL_DEVELOPMENT_API_URL
        scope: RUN_AND_BUILD_TIME
        value: "https://api.rd.usda.gov/v1"
      - key: NRCS_API_KEY
        scope: RUN_AND_BUILD_TIME
        value: "GPHYbyfrWRyG5Ao8LcqTBnV2p7SkopGotJylzXAo"
      - key: NRCS_API_URL
        scope: RUN_AND_BUILD_TIME
        value: "https://api.nrcs.usda.gov/v1"
      - key: NIFA_API_KEY
        scope: RUN_AND_BUILD_TIME
        value: "GPHYbyfrWRyG5Ao8LcqTBnV2p7SkopGotJylzXAo"
      - key: NIFA_API_URL
        scope: RUN_AND_BUILD_TIME
        value: "https://api.nifa.usda.gov/v1"
      - key: RMA_API_KEY
        scope: RUN_AND_BUILD_TIME
        value: "GPHYbyfrWRyG5Ao8LcqTBnV2p7SkopGotJylzXAo"
      - key: RMA_API_URL
        scope: RUN_AND_BUILD_TIME
        value: "https://api.rma.usda.gov/v1"
      - key: AMS_API_KEY
        scope: RUN_AND_BUILD_TIME
        value: "GPHYbyfrWRyG5Ao8LcqTBnV2p7SkopGotJylzXAo"
      - key: AMS_API_URL
        scope: RUN_AND_BUILD_TIME
        value: "https://api.ams.usda.gov/v1"
      - key: DATA_GOV_API_KEY
        scope: RUN_AND_BUILD_TIME
        value: "GPHYbyfrWRyG5Ao8LcqTBnV2p7SkopGotJylzXAo"
      - key: DATA_GOV_API_URL
        scope: RUN_AND_BUILD_TIME
        value: "https://api.data.gov"
      - key: USDA_NRCS_SOIL_API_URL
        scope: RUN_AND_BUILD_TIME
        value: "https://sdmdataaccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx"
      - key: TWILIO_ACCOUNT_SID
        scope: RUN_AND_BUILD_TIME
        value: "**********************************"
      - key: TWILIO_AUTH_TOKEN
        scope: RUN_AND_BUILD_TIME
        value: "2308c8566dc95cfe6923ae6f40d07223"
      - key: TWILIO_PHONE_NUMBER
        scope: RUN_AND_BUILD_TIME
        value: "+***********"
      - key: MATRIX_SERVER_URL
        scope: RUN_AND_BUILD_TIME
        value: "https://matrix.chat.nxtacre.com"
      - key: MATRIX_DOMAIN
        scope: RUN_AND_BUILD_TIME
        value: "chat.nxtacre.com"
      - key: REGISTRATION_SHARED_SECRET
        scope: RUN_AND_BUILD_TIME
        value: "cbZZn5M!*%4[>)+8cKUjr^Tu3s#RC1j2TDZp+7>V:/h?FV]65yjHuBbdwohEBS""
      - key: MATRIX_ADMIN_USERNAME
        scope: RUN_AND_BUILD_TIME
        value: "admin"
      - key: MATRIX_ADMIN_PASSWORD
        scope: RUN_AND_BUILD_TIME
        value: "Bap2539*"
    github:
      branch: main
      deploy_on_push: true
      repo: bap72190/nxtacre
    http_port: 3002
    instance_count: 1
    instance_size_slug: apps-s-1vcpu-1gb-fixed
    name: web
    source_dir: webapp
