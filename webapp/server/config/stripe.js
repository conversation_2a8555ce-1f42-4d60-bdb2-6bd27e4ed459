import { Stripe } from 'stripe';
import dotenv from 'dotenv';

dotenv.config();

// Stripe environment configuration
const STRIPE_SECRET_KEY = process.env.STRIPE_SECRET_KEY;
const STRIPE_PUBLISHABLE_KEY = process.env.STRIPE_PUBLISHABLE_KEY;

// Configure Stripe client only if API key is provided
let stripe = null;
if (STRIPE_SECRET_KEY && STRIPE_SECRET_KEY !== 'sk_test_placeholder_key') {
  stripe = new Stripe(STRIPE_SECRET_KEY, {
    apiVersion: '2023-10-16', // Use the latest stable API version
  });
} else {
  console.warn('Stripe API key not configured. Stripe functionality will be disabled.');
}

export { stripe, STRIPE_PUBLISHABLE_KEY };