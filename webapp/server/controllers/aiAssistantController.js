import { validationResult } from 'express-validator';
import { sequelize } from '../config/database.js';
import { handleServerError } from '../utils/errorHandlers.js';
import { withDatabaseRetry } from '../utils/retryUtils.js';
import { getSchema } from '../utils/schemaUtils.js';
import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

/**
 * Get AI assistant response
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getAIResponse = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { query, farmId } = req.body;

    // Check if query is provided
    if (!query) {
      return res.status(400).json({ message: 'Query is required' });
    }

    // Check if OpenAI API key is configured
    if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'REPLACE_WITH_REAL_API_KEY') {
      console.error('OpenAI API key is not configured or is using a placeholder value');
      return res.status(500).json({ message: 'AI service is not properly configured. Please set a valid OpenAI API key.' });
    }

    try {
      // Determine if this is an authenticated request with a user
      const isAuthenticated = req.user && req.user.id;
      const userId = isAuthenticated ? req.user.id : null;

      // Default farm info for external API requests
      let farmInfo = { name: 'Unknown', farm_type: 'Unknown', location: 'Unknown' };

      // Get farm information if farmId is provided
      if (farmId) {
        try {
          const schema = getSchema();
          const farmResult = await withDatabaseRetry(async () => {
            // Set search_path to use the specified schema
            await sequelize.query(`SET search_path TO ${schema};`);

            return sequelize.query(
              'SELECT name, farm_type, location FROM farms WHERE id = $1',
              { 
                replacements: [farmId],
                type: sequelize.QueryTypes.SELECT
              }
            );
          });

          if (farmResult.length > 0) {
            farmInfo = farmResult[0];
          }
        } catch (dbError) {
          console.error('Error fetching farm information:', dbError);
          // Continue with default farm info
        }
      }

      // Create a system message with context about farming and the specific farm
      let systemMessage = `You are an AI farming assistant for NxtAcre, a farm management platform. 
You provide helpful, accurate, and practical advice on farming practices, crop management, equipment maintenance, 
weather impacts, market trends, and other agricultural topics.`;

      // Add farm-specific context if available
      if (farmId) {
        systemMessage += `
You are currently assisting a user who manages a farm named "${farmInfo.name}" of type "${farmInfo.farm_type}" 
located in "${farmInfo.location}". Tailor your responses to be relevant to their specific farm type and location when possible.`;
      } else {
        systemMessage += `
You are currently assisting a user with general farming questions. Provide general advice that would be applicable to most farming situations.`;
      }

      systemMessage += `
Keep your responses concise, practical, and focused on providing actionable advice.`;

      // Call OpenAI API to generate a response
      const completion = await openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          { role: "system", content: systemMessage },
          { role: "user", content: query }
        ],
        max_tokens: 500,
        temperature: 0.7,
      });

      const response = completion.choices[0].message.content.trim();

      // Log the query for future training if authenticated
      if (isAuthenticated && farmId) {
        try {
          const schema = getSchema();
          await withDatabaseRetry(async () => {
            // Set search_path to use the specified schema
            await sequelize.query(`SET search_path TO ${schema};`);

            return sequelize.query(
              'INSERT INTO ai_assistant_queries (user_id, farm_id, query, response) VALUES ($1, $2, $3, $4)',
              { replacements: [userId, farmId, query, response] }
            );
          });
        } catch (logError) {
          console.error('Error logging AI assistant query:', logError);
          // Continue without logging
        }
      }

      return res.status(200).json({ response });
    } catch (aiError) {
      console.error('Error in AI assistant query:', aiError);

      // Check if this is a database error
      if (aiError.name && aiError.name.includes('Sequelize')) {
        return handleServerError(res, aiError);
      }

      // Fallback to keyword-based responses if OpenAI API call fails
      let response = "I'm sorry, I'm having trouble connecting to my knowledge base right now. ";

      // Check for specific keywords first
      if (query.toLowerCase().includes('crop rotation')) {
        response = "Crop rotation is a practice of growing different types of crops in the same area across seasons. It helps in reducing soil erosion, increasing soil fertility and crop yield. For your farm, I recommend rotating between legumes, grains, and root vegetables for optimal soil health.";
      } else if (query.toLowerCase().includes('pest')) {
        response = "Common pests in farming include aphids, beetles, and caterpillars. Integrated Pest Management (IPM) combines biological controls, habitat manipulation, and resistant crop varieties with judicious use of pesticides. For your specific crops, consider introducing beneficial insects like ladybugs or lacewings.";
      } else if (query.toLowerCase().includes('fertilizer') || query.toLowerCase().includes('nutrient')) {
        response = "Proper fertilization depends on your soil composition and crop needs. Based on typical soil tests in your region, a balanced NPK fertilizer with micronutrients would be beneficial. Consider soil testing for more precise recommendations.";
      } else if (query.toLowerCase().includes('weather') || query.toLowerCase().includes('forecast')) {
        response = "Weather patterns affect planting, irrigation, and harvesting decisions. Based on historical data for your region, the coming season is expected to be slightly warmer than average. Consider drought-resistant varieties and efficient irrigation systems.";
      } else if (query.toLowerCase().includes('market') || query.toLowerCase().includes('price')) {
        response = "Current market trends show increasing demand for organic produce and specialty crops. Prices for conventional grains remain stable with slight seasonal variations. Consider diversifying your crop portfolio to include some high-value specialty items.";
      } else if (query.toLowerCase().includes('equipment') || query.toLowerCase().includes('machinery')) {
        response = "Regular equipment maintenance extends machinery life and prevents costly breakdowns during critical periods. For your farm size, consider equipment sharing programs for specialized machinery that isn't used year-round.";
      } else if (query.toLowerCase().includes('decision support')) {
        response = "Our Decision Support System analyzes your farm data to provide AI-driven recommendations for farming operations. You can view these recommendations in the Decision Support tab of the AI Assistant page.";
      } else if (query.toLowerCase().includes('predictive maintenance')) {
        response = "Our Predictive Maintenance system uses AI to predict when your equipment might need maintenance before failures occur. You can view these predictions in the Predictive Maintenance tab of the AI Assistant page.";
      } else {
        // For queries that don't match specific keywords, provide a general farming-related response
        // based on the topic detected in the query
        const queryLower = query.toLowerCase();

        if (queryLower.includes('plant') || queryLower.includes('seed') || queryLower.includes('sow')) {
          response += "For planting, consider your local climate zone, soil conditions, and seasonal timing. Proper seed spacing and depth are crucial for optimal germination and growth.";
        } else if (queryLower.includes('harvest') || queryLower.includes('yield')) {
          response += "Harvesting at the right time maximizes both yield and quality. For most crops, early morning harvesting reduces stress and preserves freshness.";
        } else if (queryLower.includes('soil') || queryLower.includes('dirt') || queryLower.includes('ground')) {
          response += "Healthy soil is the foundation of successful farming. Regular soil testing, proper pH management, and organic matter incorporation will improve your soil structure and fertility over time.";
        } else if (queryLower.includes('water') || queryLower.includes('irrigation') || queryLower.includes('rain')) {
          response += "Efficient water management is critical for sustainable farming. Consider drip irrigation systems to reduce water usage while ensuring crops receive adequate moisture.";
        } else if (queryLower.includes('organic') || queryLower.includes('natural')) {
          response += "Organic farming practices focus on building soil health and biodiversity. Crop rotation, cover cropping, and natural pest management are key components of successful organic systems.";
        } else if (queryLower.includes('livestock') || queryLower.includes('animal') || queryLower.includes('cattle') || queryLower.includes('sheep') || queryLower.includes('chicken')) {
          response += "Livestock management requires attention to nutrition, housing, and health monitoring. Rotational grazing can improve pasture quality and animal health.";
        } else {
          // Default response for any other query
          response += "Based on general farming best practices, I recommend focusing on soil health, water conservation, and integrated pest management for sustainable farm operations. Regular monitoring and record-keeping will help you identify trends and make data-driven decisions.";
        }
      }

      // Log the query with fallback response
      await sequelize.query(
        'INSERT INTO ai_assistant_queries (user_id, farm_id, query, response) VALUES ($1, $2, $3, $4)',
        { replacements: [userId, farmId, query, response] }
      );

      return res.status(200).json({ response });
    }
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Get AI assistant query history
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getQueryHistory = async (req, res) => {
  try {
    const userId = req.user.id;
    const farmId = req.params.farmId;

    const result = await sequelize.query(
      'SELECT * FROM ai_assistant_queries WHERE user_id = $1 AND farm_id = $2 ORDER BY created_at DESC LIMIT 50',
      { 
        replacements: [userId, farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    return res.status(200).json({ queries: result });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Get AI assistant suggestions based on farm data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getSuggestions = async (req, res) => {
  try {
    const farmId = req.params.farmId;

    // Check if OpenAI API key is configured
    if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'REPLACE_WITH_REAL_API_KEY') {
      console.error('OpenAI API key is not configured or is using a placeholder value');

      // Fallback to static suggestions
      const staticSuggestions = [
        "Based on your soil data, consider adding more nitrogen to fields 3 and 4.",
        "Your equipment maintenance records show the tractor is due for service in 2 weeks.",
        "Weather forecast shows potential drought conditions. Consider adjusting irrigation schedules.",
        "Market prices for corn are trending upward. Consider reviewing your sales strategy.",
        "Based on your crop rotation history, field 2 would benefit from planting legumes this season.",
        "Check out our new Decision Support System for AI-driven farming recommendations.",
        "Use our Predictive Maintenance feature to prevent equipment failures before they happen."
      ];

      return res.status(200).json({ suggestions: staticSuggestions });
    }

    try {
      // Get farm information
      const farmResult = await sequelize.query(
        'SELECT name, farm_type, location FROM farms WHERE id = $1',
        { 
          replacements: [farmId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      const farmInfo = farmResult.length > 0 ? farmResult[0] : { name: 'Unknown', farm_type: 'Unknown', location: 'Unknown' };

      // Get recent crops data
      const cropsResult = await sequelize.query(
        'SELECT crop_name FROM crops WHERE farm_id = $1 ORDER BY created_at DESC LIMIT 5',
        { 
          replacements: [farmId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      const crops = cropsResult.map(crop => crop.crop_name).join(', ');

      // Get equipment data
      const equipmentResult = await sequelize.query(
        'SELECT name, last_maintenance_date FROM equipment WHERE farm_id = $1 ORDER BY last_maintenance_date ASC LIMIT 5',
        { 
          replacements: [farmId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      // Create a prompt for generating suggestions
      const prompt = `Generate 7 specific, actionable farming suggestions for a farm with the following details:
- Farm name: ${farmInfo.name}
- Farm type: ${farmInfo.farm_type}
- Location: ${farmInfo.location}
- Main crops: ${crops || 'Unknown'}
- Equipment: ${equipmentResult.map(eq => eq.name).join(', ') || 'Unknown'}

The suggestions should be specific, practical, and relevant to the farm's context. 
Include suggestions about crop management, equipment maintenance, weather considerations, market trends, and soil health.
Format each suggestion as a single sentence starting with an action verb. 
Do not include bullet points or numbers.`;

      // Call OpenAI API to generate suggestions
      const completion = await openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          { role: "system", content: "You are an AI farming assistant that provides specific, actionable suggestions for farm management." },
          { role: "user", content: prompt }
        ],
        max_tokens: 500,
        temperature: 0.7,
      });

      // Parse the response to get individual suggestions
      const aiResponse = completion.choices[0].message.content.trim();
      let suggestions = aiResponse.split(/\n+/).filter(line => line.trim() !== '');

      // Clean up suggestions (remove numbers, bullets, etc.)
      suggestions = suggestions.map(suggestion => 
        suggestion.replace(/^[•\-\d\.\s]+/, '').trim()
      );

      // Limit to 7 suggestions
      suggestions = suggestions.slice(0, 7);

      // Add default suggestions if we don't have enough
      if (suggestions.length < 7) {
        const defaultSuggestions = [
          "Check out our new Decision Support System for AI-driven farming recommendations.",
          "Use our Predictive Maintenance feature to prevent equipment failures before they happen.",
          "Consider reviewing your crop rotation strategy for the upcoming season.",
          "Analyze your soil test results to optimize fertilizer application.",
          "Monitor market trends to identify the best time to sell your harvest."
        ];

        while (suggestions.length < 7) {
          suggestions.push(defaultSuggestions[suggestions.length % defaultSuggestions.length]);
        }
      }

      return res.status(200).json({ suggestions });
    } catch (aiError) {
      console.error('Error calling OpenAI API for suggestions:', aiError);

      // Fallback to static suggestions if OpenAI API call fails
      const staticSuggestions = [
        "Based on your soil data, consider adding more nitrogen to fields 3 and 4.",
        "Your equipment maintenance records show the tractor is due for service in 2 weeks.",
        "Weather forecast shows potential drought conditions. Consider adjusting irrigation schedules.",
        "Market prices for corn are trending upward. Consider reviewing your sales strategy.",
        "Based on your crop rotation history, field 2 would benefit from planting legumes this season.",
        "Check out our new Decision Support System for AI-driven farming recommendations.",
        "Use our Predictive Maintenance feature to prevent equipment failures before they happen."
      ];

      return res.status(200).json({ suggestions: staticSuggestions });
    }
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Get AI decision support recommendations for a farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getDecisionSupport = async (req, res) => {
  try {
    const farmId = req.params.farmId;

    const result = await sequelize.query(
      'SELECT * FROM ai_decision_support WHERE farm_id = $1 ORDER BY created_at DESC',
      { 
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    // If no recommendations exist yet, generate new ones
    if (result.length === 0) {
      let mockRecommendations = [];

      // Try to generate AI-powered recommendations if OpenAI API key is configured and not a placeholder
      if (process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'REPLACE_WITH_REAL_API_KEY') {
        try {
          // Get farm information
          const farmResult = await sequelize.query(
            'SELECT name, farm_type, location FROM farms WHERE id = $1',
            { 
              replacements: [farmId],
              type: sequelize.QueryTypes.SELECT
            }
          );

          const farmInfo = farmResult.length > 0 ? farmResult[0] : { name: 'Unknown', farm_type: 'Unknown', location: 'Unknown' };

          // Get recent crops data
          const cropsResult = await sequelize.query(
            'SELECT crop_name FROM crops WHERE farm_id = $1 ORDER BY created_at DESC LIMIT 5',
            { 
              replacements: [farmId],
              type: sequelize.QueryTypes.SELECT
            }
          );

          const crops = cropsResult.map(crop => crop.crop_name).join(', ');

          // Get soil data if available
          const soilResult = await sequelize.query(
            'SELECT soil_type, ph_level, organic_matter FROM soil_tests WHERE farm_id = $1 ORDER BY test_date DESC LIMIT 1',
            { 
              replacements: [farmId],
              type: sequelize.QueryTypes.SELECT
            }
          );

          const soilInfo = soilResult.length > 0 ? soilResult[0] : null;

          // Create a prompt for generating decision support recommendations
          const prompt = `Generate 5 detailed farming operation recommendations for a farm with the following details:
- Farm name: ${farmInfo.name}
- Farm type: ${farmInfo.farm_type}
- Location: ${farmInfo.location}
- Main crops: ${crops || 'Unknown'}
${soilInfo ? `- Soil type: ${soilInfo.soil_type}
- Soil pH: ${soilInfo.ph_level}
- Organic matter: ${soilInfo.organic_matter}%` : ''}

For each recommendation, provide:
1. Operation type (e.g., Planting, Irrigation, Fertilization, Pest Management, Harvesting)
2. A detailed recommendation (1-2 sentences)
3. Factors considered in making this recommendation
4. A confidence score (a number between 70 and 95)

Format your response as a JSON array with objects containing these fields:
[
  {
    "operation_type": "...",
    "recommendation": "...",
    "factors_considered": "...",
    "confidence_score": 85.5
  },
  ...
]`;

          // Call OpenAI API to generate recommendations
          const completion = await openai.chat.completions.create({
            model: "gpt-3.5-turbo",
            messages: [
              { role: "system", content: "You are an AI farming assistant that provides data-driven recommendations for farm operations." },
              { role: "user", content: prompt }
            ],
            max_tokens: 1000,
            temperature: 0.7,
            response_format: { type: "json_object" }
          });

          // Parse the response to get recommendations
          const aiResponse = completion.choices[0].message.content.trim();
          try {
            const parsedResponse = JSON.parse(aiResponse);
            if (Array.isArray(parsedResponse) || (parsedResponse.recommendations && Array.isArray(parsedResponse.recommendations))) {
              const recommendations = Array.isArray(parsedResponse) ? parsedResponse : parsedResponse.recommendations;

              // Convert AI recommendations to our format
              mockRecommendations = recommendations.map(rec => ({
                operation_type: rec.operation_type,
                recommendation: rec.recommendation,
                confidence_score: parseFloat(rec.confidence_score) || Math.floor(Math.random() * 15) + 80, // Fallback to random score between 80-95
                factors_considered: rec.factors_considered,
                is_implemented: false,
                created_at: new Date(),
                updated_at: new Date()
              }));
            }
          } catch (parseError) {
            console.error('Error parsing OpenAI response:', parseError);
            // Will fall back to default recommendations below
          }
        } catch (aiError) {
          console.error('Error calling OpenAI API for decision support:', aiError);
          // Will fall back to default recommendations below
        }
      }

      // If we couldn't generate AI recommendations, use default ones
      if (mockRecommendations.length === 0) {
        mockRecommendations = [
          {
            operation_type: 'Planting',
            recommendation: 'Based on soil moisture levels and weather forecast, delay corn planting by 5 days for optimal germination conditions.',
            confidence_score: 85.5,
            factors_considered: 'Soil moisture, 10-day weather forecast, historical germination rates',
            is_implemented: false,
            created_at: new Date(),
            updated_at: new Date()
          },
          {
            operation_type: 'Irrigation',
            recommendation: 'Increase irrigation in the eastern fields by 15% to compensate for forecasted dry conditions.',
            confidence_score: 92.3,
            factors_considered: 'Current soil moisture, evapotranspiration rates, 7-day weather forecast',
            is_implemented: false,
            created_at: new Date(),
            updated_at: new Date()
          },
          {
            operation_type: 'Fertilization',
            recommendation: 'Apply nitrogen fertilizer to wheat fields at 75% of normal rate due to residual nitrogen detected in soil tests.',
            confidence_score: 88.7,
            factors_considered: 'Recent soil tests, crop growth stage, historical yield data',
            is_implemented: false,
            created_at: new Date(),
            updated_at: new Date()
          },
          {
            operation_type: 'Pest Management',
            recommendation: 'Deploy beneficial nematodes in southern fields to address emerging grub population before damage occurs.',
            confidence_score: 78.2,
            factors_considered: 'Recent pest scouting reports, soil temperature, historical pest patterns',
            is_implemented: false,
            created_at: new Date(),
            updated_at: new Date()
          },
          {
            operation_type: 'Harvesting',
            recommendation: 'Prioritize harvesting of field 7 within the next 48 hours to avoid forecasted heavy rain that could reduce crop quality.',
            confidence_score: 94.1,
            factors_considered: 'Crop maturity, weather forecast, equipment availability',
            is_implemented: false,
            created_at: new Date(),
            updated_at: new Date()
          }
        ];
      }

      // Insert mock recommendations
      for (const rec of mockRecommendations) {
        await sequelize.query(
          `INSERT INTO ai_decision_support 
           (farm_id, operation_type, recommendation, confidence_score, factors_considered, is_implemented, created_at, updated_at) 
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
          { 
            replacements: [
              farmId, 
              rec.operation_type, 
              rec.recommendation, 
              rec.confidence_score, 
              rec.factors_considered, 
              rec.is_implemented, 
              rec.created_at, 
              rec.updated_at
            ] 
          }
        );
      }

      // Fetch the newly inserted recommendations
      const newResult = await sequelize.query(
        'SELECT * FROM ai_decision_support WHERE farm_id = $1 ORDER BY created_at DESC',
        { 
          replacements: [farmId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      return res.status(200).json({ recommendations: newResult });
    }

    return res.status(200).json({ recommendations: result });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Create a new AI decision support recommendation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const createDecisionSupport = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { farmId, operationType, recommendation, confidenceScore, factorsConsidered } = req.body;
    const now = new Date();

    const result = await sequelize.query(
      `INSERT INTO ai_decision_support 
       (farm_id, operation_type, recommendation, confidence_score, factors_considered, created_at, updated_at) 
       VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *`,
      { 
        replacements: [farmId, operationType, recommendation, confidenceScore, factorsConsidered, now, now],
        type: sequelize.QueryTypes.INSERT
      }
    );

    return res.status(201).json({ recommendation: result[0][0] });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Update an AI decision support recommendation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const updateDecisionSupport = async (req, res) => {
  try {
    const id = req.params.id;
    const { isImplemented } = req.body;
    const now = new Date();

    const result = await sequelize.query(
      `UPDATE ai_decision_support 
       SET is_implemented = $1, updated_at = $2
       WHERE id = $3 RETURNING *`,
      { 
        replacements: [isImplemented, now, id],
        type: sequelize.QueryTypes.UPDATE
      }
    );

    if (result[1] === 0) {
      return res.status(404).json({ message: 'Recommendation not found' });
    }

    return res.status(200).json({ recommendation: result[0][0] });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Get AI predictive maintenance recommendations for a farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getPredictiveMaintenance = async (req, res) => {
  try {
    const farmId = req.params.farmId;

    const result = await sequelize.query(
      `SELECT pm.*, e.name as equipment_name 
       FROM ai_predictive_maintenance pm
       JOIN equipment e ON pm.equipment_id = e.id
       WHERE pm.farm_id = $1 
       ORDER BY pm.urgency_level DESC, pm.predicted_failure_date ASC`,
      { 
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    // If no predictions exist yet, generate some mock ones
    if (result.length === 0) {
      // First, get some equipment IDs for this farm
      const equipmentResult = await sequelize.query(
        'SELECT id, name FROM equipment WHERE farm_id = $1 LIMIT 5',
        { 
          replacements: [farmId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      // If no equipment exists, return empty array
      if (equipmentResult.length === 0) {
        return res.status(200).json({ predictions: [] });
      }

      // Generate predictions for equipment
      let mockPredictions = [];

      // Try to generate AI-powered predictions if OpenAI API key is configured
      if (process.env.OPENAI_API_KEY && equipmentResult.length > 0) {
        try {
          // Get farm information
          const farmResult = await sequelize.query(
            'SELECT name, farm_type, location FROM farms WHERE id = $1',
            { 
              replacements: [farmId],
              type: sequelize.QueryTypes.SELECT
            }
          );

          const farmInfo = farmResult.length > 0 ? farmResult[0] : { name: 'Unknown', farm_type: 'Unknown', location: 'Unknown' };

          // Get equipment usage data if available
          const usageData = [];
          for (const equipment of equipmentResult) {
            const usageResult = await sequelize.query(
              'SELECT hours_used, last_maintenance_date, maintenance_interval FROM equipment_usage WHERE equipment_id = $1 ORDER BY recorded_date DESC LIMIT 1',
              { 
                replacements: [equipment.id],
                type: sequelize.QueryTypes.SELECT
              }
            );

            if (usageResult.length > 0) {
              usageData.push({
                equipment_id: equipment.id,
                equipment_name: equipment.name,
                hours_used: usageResult[0].hours_used,
                last_maintenance_date: usageResult[0].last_maintenance_date,
                maintenance_interval: usageResult[0].maintenance_interval
              });
            } else {
              usageData.push({
                equipment_id: equipment.id,
                equipment_name: equipment.name,
                hours_used: 'Unknown',
                last_maintenance_date: 'Unknown',
                maintenance_interval: 'Unknown'
              });
            }
          }

          // Create a prompt for generating predictive maintenance recommendations
          const prompt = `Generate predictive maintenance recommendations for farm equipment based on the following details:
- Farm name: ${farmInfo.name}
- Farm type: ${farmInfo.farm_type}
- Location: ${farmInfo.location}

Equipment details:
${usageData.map(eq => `
- Equipment ID: ${eq.equipment_id}
- Equipment Name: ${eq.equipment_name}
- Hours Used: ${eq.hours_used}
- Last Maintenance Date: ${eq.last_maintenance_date}
- Maintenance Interval: ${eq.maintenance_interval}
`).join('')}

For each piece of equipment, provide:
1. Equipment ID
2. Maintenance type (e.g., Oil Change, Belt Replacement, Hydraulic System, Cooling System)
3. A detailed prediction of potential issues and recommended maintenance
4. Urgency level (High, Medium, or Low)
5. Predicted days until failure (a number between 7 and 90)
6. Confidence score (a number between 70 and 95)

Format your response as a JSON array with objects containing these fields:
[
  {
    "equipment_id": "...",
    "maintenance_type": "...",
    "prediction": "...",
    "urgency_level": "...",
    "days_until_failure": 30,
    "confidence_score": 85.5
  },
  ...
]

Limit your response to one prediction per piece of equipment, focusing on the most critical maintenance need.`;

          // Call OpenAI API to generate predictions
          const completion = await openai.chat.completions.create({
            model: "gpt-3.5-turbo",
            messages: [
              { role: "system", content: "You are an AI farming assistant that specializes in predictive maintenance for agricultural equipment." },
              { role: "user", content: prompt }
            ],
            max_tokens: 1000,
            temperature: 0.7,
            response_format: { type: "json_object" }
          });

          // Parse the response to get predictions
          const aiResponse = completion.choices[0].message.content.trim();
          try {
            const parsedResponse = JSON.parse(aiResponse);
            if (Array.isArray(parsedResponse) || (parsedResponse.predictions && Array.isArray(parsedResponse.predictions))) {
              const predictions = Array.isArray(parsedResponse) ? parsedResponse : parsedResponse.predictions;

              // Convert AI predictions to our format
              mockPredictions = predictions.map(pred => {
                // Calculate predicted failure date based on days_until_failure
                const daysUntilFailure = parseInt(pred.days_until_failure) || 30;
                const predictedFailureDate = new Date(Date.now() + daysUntilFailure * 24 * 60 * 60 * 1000);

                return {
                  equipment_id: pred.equipment_id,
                  maintenance_type: pred.maintenance_type,
                  prediction: pred.prediction,
                  urgency_level: pred.urgency_level,
                  predicted_failure_date: predictedFailureDate,
                  confidence_score: parseFloat(pred.confidence_score) || Math.floor(Math.random() * 15) + 80, // Fallback to random score between 80-95
                  is_addressed: false,
                  created_at: new Date(),
                  updated_at: new Date()
                };
              });
            }
          } catch (parseError) {
            console.error('Error parsing OpenAI response for predictive maintenance:', parseError);
            // Will fall back to default predictions below
          }
        } catch (aiError) {
          console.error('Error calling OpenAI API for predictive maintenance:', aiError);
          // Will fall back to default predictions below
        }
      }

      // If we couldn't generate AI predictions, use default ones
      if (mockPredictions.length === 0 && equipmentResult.length > 0) {
        mockPredictions = [
          {
            equipment_id: equipmentResult[0].id,
            maintenance_type: 'Oil Change',
            prediction: 'Engine oil analysis indicates increased metal particles. Recommend oil change within 50 operating hours.',
            urgency_level: 'Medium',
            predicted_failure_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
            confidence_score: 87.5,
            is_addressed: false,
            created_at: new Date(),
            updated_at: new Date()
          }
        ];

        // Add more predictions if we have more equipment
        if (equipmentResult.length > 1) {
          mockPredictions.push({
            equipment_id: equipmentResult[1].id,
            maintenance_type: 'Belt Replacement',
            prediction: 'Drive belt showing signs of wear. Replacement recommended before harvest season.',
            urgency_level: 'Low',
            predicted_failure_date: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
            confidence_score: 75.2,
            is_addressed: false,
            created_at: new Date(),
            updated_at: new Date()
          });
        }

        if (equipmentResult.length > 2) {
          mockPredictions.push({
            equipment_id: equipmentResult[2].id,
            maintenance_type: 'Hydraulic System',
            prediction: 'Hydraulic pressure fluctuations detected. Inspect hydraulic pump and lines immediately.',
            urgency_level: 'High',
            predicted_failure_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
            confidence_score: 92.8,
            is_addressed: false,
            created_at: new Date(),
            updated_at: new Date()
          });
        }

        if (equipmentResult.length > 3) {
          mockPredictions.push({
            equipment_id: equipmentResult[3].id,
            maintenance_type: 'Cooling System',
            prediction: 'Temperature sensor data indicates potential coolant leak. Check coolant levels and inspect for leaks.',
            urgency_level: 'Medium',
            predicted_failure_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
            confidence_score: 83.1,
            is_addressed: false,
            created_at: new Date(),
            updated_at: new Date()
          });
        }
      }

      // Insert mock predictions
      for (const pred of mockPredictions) {
        await sequelize.query(
          `INSERT INTO ai_predictive_maintenance 
           (farm_id, equipment_id, maintenance_type, prediction, urgency_level, predicted_failure_date, confidence_score, is_addressed, created_at, updated_at) 
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
          { 
            replacements: [
              farmId, 
              pred.equipment_id, 
              pred.maintenance_type, 
              pred.prediction, 
              pred.urgency_level, 
              pred.predicted_failure_date, 
              pred.confidence_score, 
              pred.is_addressed, 
              pred.created_at, 
              pred.updated_at
            ] 
          }
        );
      }

      // Fetch the newly inserted predictions with equipment names
      const newResult = await sequelize.query(
        `SELECT pm.*, e.name as equipment_name 
         FROM ai_predictive_maintenance pm
         JOIN equipment e ON pm.equipment_id = e.id
         WHERE pm.farm_id = $1 
         ORDER BY pm.urgency_level DESC, pm.predicted_failure_date ASC`,
        { 
          replacements: [farmId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      return res.status(200).json({ predictions: newResult });
    }

    return res.status(200).json({ predictions: result });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Create a new AI predictive maintenance recommendation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const createPredictiveMaintenance = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { 
      farmId, 
      equipmentId, 
      maintenanceType, 
      prediction, 
      urgencyLevel, 
      predictedFailureDate, 
      confidenceScore 
    } = req.body;

    const now = new Date();

    const result = await sequelize.query(
      `INSERT INTO ai_predictive_maintenance 
       (farm_id, equipment_id, maintenance_type, prediction, urgency_level, predicted_failure_date, confidence_score, created_at, updated_at) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) RETURNING *`,
      { 
        replacements: [
          farmId, 
          equipmentId, 
          maintenanceType, 
          prediction, 
          urgencyLevel, 
          predictedFailureDate || null, 
          confidenceScore, 
          now, 
          now
        ],
        type: sequelize.QueryTypes.INSERT
      }
    );

    return res.status(201).json({ prediction: result[0][0] });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Update an AI predictive maintenance recommendation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const updatePredictiveMaintenance = async (req, res) => {
  try {
    const id = req.params.id;
    const { isAddressed } = req.body;
    const now = new Date();

    const result = await sequelize.query(
      `UPDATE ai_predictive_maintenance 
       SET is_addressed = $1, updated_at = $2
       WHERE id = $3 RETURNING *`,
      { 
        replacements: [isAddressed, now, id],
        type: sequelize.QueryTypes.UPDATE
      }
    );

    if (result[1] === 0) {
      return res.status(404).json({ message: 'Prediction not found' });
    }

    return res.status(200).json({ prediction: result[0][0] });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Get AI harvest timing recommendations for a farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getHarvestRecommendations = async (req, res) => {
  try {
    const farmId = req.params.farmId;

    const result = await sequelize.query(
      'SELECT * FROM ai_harvest_recommendations WHERE farm_id = $1 ORDER BY created_at DESC',
      { 
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    // If no recommendations exist yet, generate new ones
    if (result.length === 0) {
      let harvestRecommendations = [];

      // Try to generate AI-powered recommendations if OpenAI API key is configured and not a placeholder
      if (process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'REPLACE_WITH_REAL_API_KEY') {
        try {
          // Get farm information
          const farmResult = await sequelize.query(
            'SELECT name, farm_type, location FROM farms WHERE id = $1',
            { 
              replacements: [farmId],
              type: sequelize.QueryTypes.SELECT
            }
          );

          const farmInfo = farmResult.length > 0 ? farmResult[0] : { name: 'Unknown', farm_type: 'Unknown', location: 'Unknown' };

          // Get fields data
          const fieldsResult = await sequelize.query(
            'SELECT id, name, crop_type, size, size_unit FROM fields WHERE farm_id = $1',
            { 
              replacements: [farmId],
              type: sequelize.QueryTypes.SELECT
            }
          );

          // Get crops data
          const cropsResult = await sequelize.query(
            'SELECT id, crop_name, planting_date, expected_harvest_date FROM crops WHERE farm_id = $1',
            { 
              replacements: [farmId],
              type: sequelize.QueryTypes.SELECT
            }
          );

          // Get weather data if available
          const weatherResult = await sequelize.query(
            `SELECT w.* FROM weather w
             JOIN fields f ON w.field_id = f.id
             WHERE f.farm_id = $1
             ORDER BY w.date DESC
             LIMIT 10`,
            { 
              replacements: [farmId],
              type: sequelize.QueryTypes.SELECT
            }
          );

          // Get historical harvest data
          const harvestResult = await sequelize.query(
            `SELECT h.*, f.name as field_name, c.crop_name 
             FROM harvests h
             JOIN fields f ON h.field_id = f.id
             JOIN crops c ON h.crop_id = c.id
             WHERE h.farm_id = $1
             ORDER BY h.actual_date DESC
             LIMIT 20`,
            { 
              replacements: [farmId],
              type: sequelize.QueryTypes.SELECT
            }
          );

          // Create a prompt for generating harvest timing recommendations
          const prompt = `Generate 5 detailed harvest timing recommendations for a farm with the following details:
- Farm name: ${farmInfo.name}
- Farm type: ${farmInfo.farm_type}
- Location: ${farmInfo.location}
- Fields: ${fieldsResult.map(f => `${f.name} (${f.crop_type}, ${f.size} ${f.size_unit})`).join(', ')}
- Crops: ${cropsResult.map(c => `${c.crop_name} (planted: ${c.planting_date}, expected harvest: ${c.expected_harvest_date})`).join(', ')}
${weatherResult.length > 0 ? `- Recent weather: ${JSON.stringify(weatherResult.slice(0, 3))}` : ''}
${harvestResult.length > 0 ? `- Historical harvest data: ${JSON.stringify(harvestResult.slice(0, 3))}` : ''}

For each recommendation, provide:
1. Field name
2. Crop type
3. Recommended harvest date or date range
4. Detailed explanation of the recommendation (2-3 sentences)
5. Factors considered in making this recommendation
6. A confidence score (a number between 70 and 95)

Format your response as a JSON array with objects containing these fields:
[
  {
    "field_name": "...",
    "crop_type": "...",
    "harvest_date": "...",
    "explanation": "...",
    "factors_considered": "...",
    "confidence_score": 85.5
  },
  ...
]`;

          // Call OpenAI API to generate recommendations
          const completion = await openai.chat.completions.create({
            model: "gpt-3.5-turbo",
            messages: [
              { role: "system", content: "You are an AI farming assistant that specializes in harvest timing recommendations." },
              { role: "user", content: prompt }
            ],
            max_tokens: 1500,
            temperature: 0.7,
            response_format: { type: "json_object" }
          });

          // Parse the response to get recommendations
          const aiResponse = completion.choices[0].message.content.trim();
          try {
            const parsedResponse = JSON.parse(aiResponse);
            if (Array.isArray(parsedResponse) || (parsedResponse.recommendations && Array.isArray(parsedResponse.recommendations))) {
              const recommendations = Array.isArray(parsedResponse) ? parsedResponse : parsedResponse.recommendations;

              // Convert AI recommendations to our format
              harvestRecommendations = recommendations.map(rec => ({
                field_name: rec.field_name,
                crop_type: rec.crop_type,
                harvest_date: rec.harvest_date,
                explanation: rec.explanation,
                factors_considered: rec.factors_considered,
                confidence_score: parseFloat(rec.confidence_score) || Math.floor(Math.random() * 15) + 80, // Fallback to random score between 80-95
                is_implemented: false,
                created_at: new Date(),
                updated_at: new Date()
              }));
            }
          } catch (parseError) {
            console.error('Error parsing OpenAI response for harvest recommendations:', parseError);
            // Will fall back to default recommendations below
          }
        } catch (aiError) {
          console.error('Error calling OpenAI API for harvest recommendations:', aiError);
          // Will fall back to default recommendations below
        }
      }

      // If we couldn't generate AI recommendations, use default ones
      if (harvestRecommendations.length === 0) {
        // Get some field names for this farm
        const fieldsResult = await sequelize.query(
          'SELECT id, name, crop_type FROM fields WHERE farm_id = $1 LIMIT 5',
          { 
            replacements: [farmId],
            type: sequelize.QueryTypes.SELECT
          }
        );

        // If no fields exist, return empty array
        if (fieldsResult.length === 0) {
          return res.status(200).json({ recommendations: [] });
        }

        // Generate default recommendations
        const today = new Date();
        const oneWeekLater = new Date(today);
        oneWeekLater.setDate(oneWeekLater.getDate() + 7);
        const twoWeeksLater = new Date(today);
        twoWeeksLater.setDate(twoWeeksLater.getDate() + 14);
        const threeWeeksLater = new Date(today);
        threeWeeksLater.setDate(threeWeeksLater.getDate() + 21);

        harvestRecommendations = [
          {
            field_name: fieldsResult[0]?.name || 'Field 1',
            crop_type: fieldsResult[0]?.crop_type || 'Corn',
            harvest_date: oneWeekLater.toISOString().split('T')[0],
            explanation: 'Based on growing degree days and current moisture levels, the optimal harvest window begins next week. Harvesting during this period will maximize grain quality and minimize field losses.',
            factors_considered: 'Growing degree days, grain moisture content, weather forecast, historical yield data',
            confidence_score: 87.5,
            is_implemented: false,
            created_at: new Date(),
            updated_at: new Date()
          }
        ];

        // Add more recommendations if we have more fields
        if (fieldsResult.length > 1) {
          harvestRecommendations.push({
            field_name: fieldsResult[1]?.name || 'Field 2',
            crop_type: fieldsResult[1]?.crop_type || 'Soybeans',
            harvest_date: twoWeeksLater.toISOString().split('T')[0],
            explanation: 'Crop maturity indicators suggest optimal harvest in two weeks. Delaying beyond this window risks pod shattering if weather conditions deteriorate.',
            factors_considered: 'Pod color, leaf drop percentage, seed moisture content, 10-day weather forecast',
            confidence_score: 82.3,
            is_implemented: false,
            created_at: new Date(),
            updated_at: new Date()
          });
        }

        if (fieldsResult.length > 2) {
          harvestRecommendations.push({
            field_name: fieldsResult[2]?.name || 'Field 3',
            crop_type: fieldsResult[2]?.crop_type || 'Wheat',
            harvest_date: threeWeeksLater.toISOString().split('T')[0],
            explanation: 'Current grain moisture is still above optimal levels. Waiting three weeks will allow natural dry-down and reduce drying costs while maintaining quality.',
            factors_considered: 'Current grain moisture, ambient humidity trends, drying cost analysis, quality premium potential',
            confidence_score: 91.7,
            is_implemented: false,
            created_at: new Date(),
            updated_at: new Date()
          });
        }
      }

      // Insert recommendations
      for (const rec of harvestRecommendations) {
        await sequelize.query(
          `INSERT INTO ai_harvest_recommendations 
           (farm_id, field_name, crop_type, harvest_date, explanation, factors_considered, confidence_score, is_implemented, created_at, updated_at) 
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
          { 
            replacements: [
              farmId, 
              rec.field_name, 
              rec.crop_type, 
              rec.harvest_date, 
              rec.explanation, 
              rec.factors_considered, 
              rec.confidence_score, 
              rec.is_implemented, 
              rec.created_at, 
              rec.updated_at
            ] 
          }
        );
      }

      // Fetch the newly inserted recommendations
      const newResult = await sequelize.query(
        'SELECT * FROM ai_harvest_recommendations WHERE farm_id = $1 ORDER BY created_at DESC',
        { 
          replacements: [farmId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      return res.status(200).json({ recommendations: newResult });
    }

    return res.status(200).json({ recommendations: result });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Update an AI harvest recommendation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const updateHarvestRecommendation = async (req, res) => {
  try {
    const id = req.params.id;
    const { isImplemented } = req.body;
    const now = new Date();

    const result = await sequelize.query(
      `UPDATE ai_harvest_recommendations 
       SET is_implemented = $1, updated_at = $2
       WHERE id = $3 RETURNING *`,
      { 
        replacements: [isImplemented, now, id],
        type: sequelize.QueryTypes.UPDATE
      }
    );

    if (result[1] === 0) {
      return res.status(404).json({ message: 'Recommendation not found' });
    }

    return res.status(200).json({ recommendation: result[0][0] });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Get AI field improvement recommendations for a farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getFieldImprovementRecommendations = async (req, res) => {
  try {
    const farmId = req.params.farmId;

    const result = await sequelize.query(
      'SELECT * FROM ai_field_improvement_recommendations WHERE farm_id = $1 ORDER BY created_at DESC',
      { 
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    // If no recommendations exist yet, generate new ones
    if (result.length === 0) {
      let fieldRecommendations = [];

      // Try to generate AI-powered recommendations if OpenAI API key is configured and not a placeholder
      if (process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'REPLACE_WITH_REAL_API_KEY') {
        try {
          // Get farm information
          const farmResult = await sequelize.query(
            'SELECT name, farm_type, location FROM farms WHERE id = $1',
            { 
              replacements: [farmId],
              type: sequelize.QueryTypes.SELECT
            }
          );

          const farmInfo = farmResult.length > 0 ? farmResult[0] : { name: 'Unknown', farm_type: 'Unknown', location: 'Unknown' };

          // Get fields data
          const fieldsResult = await sequelize.query(
            'SELECT id, name, crop_type, size, size_unit, status, notes FROM fields WHERE farm_id = $1',
            { 
              replacements: [farmId],
              type: sequelize.QueryTypes.SELECT
            }
          );

          // Get soil sample data
          const soilSamplesResult = await sequelize.query(
            `SELECT ss.*, f.name as field_name 
             FROM soil_samples ss
             JOIN fields f ON ss.field_id = f.id
             WHERE ss.farm_id = $1
             ORDER BY ss.sample_date DESC
             LIMIT 20`,
            { 
              replacements: [farmId],
              type: sequelize.QueryTypes.SELECT
            }
          );

          // Get soil test results
          const soilTestsResult = await sequelize.query(
            `SELECT str.*, ss.field_id, f.name as field_name 
             FROM soil_test_results str
             JOIN soil_samples ss ON str.soil_sample_id = ss.id
             JOIN fields f ON ss.field_id = f.id
             WHERE ss.farm_id = $1
             ORDER BY str.created_at DESC
             LIMIT 20`,
            { 
              replacements: [farmId],
              type: sequelize.QueryTypes.SELECT
            }
          );

          // Get soil amendments
          const soilAmendmentsResult = await sequelize.query(
            `SELECT sa.*, f.name as field_name 
             FROM soil_amendments sa
             JOIN fields f ON sa.field_id = f.id
             WHERE sa.farm_id = $1
             ORDER BY sa.application_date DESC
             LIMIT 20`,
            { 
              replacements: [farmId],
              type: sequelize.QueryTypes.SELECT
            }
          );

          // Create a prompt for generating field improvement recommendations
          const prompt = `Generate 5 detailed field improvement recommendations for a farm with the following details:
- Farm name: ${farmInfo.name}
- Farm type: ${farmInfo.farm_type}
- Location: ${farmInfo.location}
- Fields: ${fieldsResult.map(f => `${f.name} (${f.crop_type}, ${f.size} ${f.size_unit}, status: ${f.status})`).join(', ')}
${soilSamplesResult.length > 0 ? `- Recent soil samples: ${JSON.stringify(soilSamplesResult.slice(0, 3))}` : ''}
${soilTestsResult.length > 0 ? `- Soil test results: ${JSON.stringify(soilTestsResult.slice(0, 3))}` : ''}
${soilAmendmentsResult.length > 0 ? `- Recent soil amendments: ${JSON.stringify(soilAmendmentsResult.slice(0, 3))}` : ''}

For each recommendation, provide:
1. Field name
2. Improvement type (e.g., Soil Amendment, Drainage, Irrigation, Crop Rotation, Cover Cropping, Tillage Practice)
3. Detailed recommendation (2-3 sentences)
4. Expected benefits (1-2 sentences)
5. Implementation timeline (e.g., "Before next planting", "Within 3 months", "During fall")
6. A confidence score (a number between 70 and 95)

Format your response as a JSON array with objects containing these fields:
[
  {
    "field_name": "...",
    "improvement_type": "...",
    "recommendation": "...",
    "expected_benefits": "...",
    "implementation_timeline": "...",
    "confidence_score": 85.5
  },
  ...
]`;

          // Call OpenAI API to generate recommendations
          const completion = await openai.chat.completions.create({
            model: "gpt-3.5-turbo",
            messages: [
              { role: "system", content: "You are an AI farming assistant that specializes in field improvement recommendations." },
              { role: "user", content: prompt }
            ],
            max_tokens: 1500,
            temperature: 0.7,
            response_format: { type: "json_object" }
          });

          // Parse the response to get recommendations
          const aiResponse = completion.choices[0].message.content.trim();
          try {
            const parsedResponse = JSON.parse(aiResponse);
            if (Array.isArray(parsedResponse) || (parsedResponse.recommendations && Array.isArray(parsedResponse.recommendations))) {
              const recommendations = Array.isArray(parsedResponse) ? parsedResponse : parsedResponse.recommendations;

              // Convert AI recommendations to our format
              fieldRecommendations = recommendations.map(rec => ({
                field_name: rec.field_name,
                improvement_type: rec.improvement_type,
                recommendation: rec.recommendation,
                expected_benefits: rec.expected_benefits,
                implementation_timeline: rec.implementation_timeline,
                confidence_score: parseFloat(rec.confidence_score) || Math.floor(Math.random() * 15) + 80, // Fallback to random score between 80-95
                is_implemented: false,
                created_at: new Date(),
                updated_at: new Date()
              }));
            }
          } catch (parseError) {
            console.error('Error parsing OpenAI response for field improvement recommendations:', parseError);
            // Will fall back to default recommendations below
          }
        } catch (aiError) {
          console.error('Error calling OpenAI API for field improvement recommendations:', aiError);
          // Will fall back to default recommendations below
        }
      }

      // If we couldn't generate AI recommendations, use default ones
      if (fieldRecommendations.length === 0) {
        // Get some field names for this farm
        const fieldsResult = await sequelize.query(
          'SELECT id, name FROM fields WHERE farm_id = $1 LIMIT 5',
          { 
            replacements: [farmId],
            type: sequelize.QueryTypes.SELECT
          }
        );

        // If no fields exist, return empty array
        if (fieldsResult.length === 0) {
          return res.status(200).json({ recommendations: [] });
        }

        // Generate default recommendations
        fieldRecommendations = [
          {
            field_name: fieldsResult[0]?.name || 'Field 1',
            improvement_type: 'Soil Amendment',
            recommendation: 'Apply lime at a rate of 2 tons per acre to address soil acidity. Recent soil tests indicate pH levels below optimal range for current crops.',
            expected_benefits: 'Increased nutrient availability and improved microbial activity, potentially increasing yield by 15-20%.',
            implementation_timeline: 'Before next planting season',
            confidence_score: 89.5,
            is_implemented: false,
            created_at: new Date(),
            updated_at: new Date()
          }
        ];

        // Add more recommendations if we have more fields
        if (fieldsResult.length > 1) {
          fieldRecommendations.push({
            field_name: fieldsResult[1]?.name || 'Field 2',
            improvement_type: 'Cover Cropping',
            recommendation: 'Plant a winter cover crop mix of cereal rye and hairy vetch after harvest. This combination provides excellent erosion control while fixing nitrogen.',
            expected_benefits: 'Reduced soil erosion, improved soil structure, and natural nitrogen fixation reducing fertilizer needs.',
            implementation_timeline: 'Immediately after harvest',
            confidence_score: 92.3,
            is_implemented: false,
            created_at: new Date(),
            updated_at: new Date()
          });
        }

        if (fieldsResult.length > 2) {
          fieldRecommendations.push({
            field_name: fieldsResult[2]?.name || 'Field 3',
            improvement_type: 'Drainage',
            recommendation: 'Install subsurface drainage tiles at 30-foot spacing. Field observations indicate waterlogging issues in the northeast section after heavy rainfall.',
            expected_benefits: 'Improved root development, earlier planting access, and reduced risk of crop stress during wet periods.',
            implementation_timeline: 'During dry summer months',
            confidence_score: 84.7,
            is_implemented: false,
            created_at: new Date(),
            updated_at: new Date()
          });
        }
      }

      // Insert recommendations
      for (const rec of fieldRecommendations) {
        await sequelize.query(
          `INSERT INTO ai_field_improvement_recommendations 
           (farm_id, field_name, improvement_type, recommendation, expected_benefits, implementation_timeline, confidence_score, is_implemented, created_at, updated_at) 
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
          { 
            replacements: [
              farmId, 
              rec.field_name, 
              rec.improvement_type, 
              rec.recommendation, 
              rec.expected_benefits, 
              rec.implementation_timeline, 
              rec.confidence_score, 
              rec.is_implemented, 
              rec.created_at, 
              rec.updated_at
            ] 
          }
        );
      }

      // Fetch the newly inserted recommendations
      const newResult = await sequelize.query(
        'SELECT * FROM ai_field_improvement_recommendations WHERE farm_id = $1 ORDER BY created_at DESC',
        { 
          replacements: [farmId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      return res.status(200).json({ recommendations: newResult });
    }

    return res.status(200).json({ recommendations: result });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Update an AI field improvement recommendation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const updateFieldImprovementRecommendation = async (req, res) => {
  try {
    const id = req.params.id;
    const { isImplemented } = req.body;
    const now = new Date();

    const result = await sequelize.query(
      `UPDATE ai_field_improvement_recommendations 
       SET is_implemented = $1, updated_at = $2
       WHERE id = $3 RETURNING *`,
      { 
        replacements: [isImplemented, now, id],
        type: sequelize.QueryTypes.UPDATE
      }
    );

    if (result[1] === 0) {
      return res.status(404).json({ message: 'Recommendation not found' });
    }

    return res.status(200).json({ recommendation: result[0][0] });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Get AI financial optimization recommendations for a farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getFinancialRecommendations = async (req, res) => {
  try {
    const farmId = req.params.farmId;

    const result = await sequelize.query(
      'SELECT * FROM ai_financial_recommendations WHERE farm_id = $1 ORDER BY created_at DESC',
      { 
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    // If no recommendations exist yet, generate new ones
    if (result.length === 0) {
      let financialRecommendations = [];

      // Try to generate AI-powered recommendations if OpenAI API key is configured and not a placeholder
      if (process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'REPLACE_WITH_REAL_API_KEY') {
        try {
          // Get farm information
          const farmResult = await sequelize.query(
            'SELECT name, farm_type, location FROM farms WHERE id = $1',
            { 
              replacements: [farmId],
              type: sequelize.QueryTypes.SELECT
            }
          );

          const farmInfo = farmResult.length > 0 ? farmResult[0] : { name: 'Unknown', farm_type: 'Unknown', location: 'Unknown' };

          // Get expense data
          const expensesResult = await sequelize.query(
            `SELECT e.category, e.amount, e.expense_date, e.status 
             FROM expenses e
             JOIN employees emp ON e.employee_id = emp.id
             JOIN user_farms uf ON emp.user_id = uf.user_id
             WHERE uf.farm_id = $1
             ORDER BY e.expense_date DESC
             LIMIT 50`,
            { 
              replacements: [farmId],
              type: sequelize.QueryTypes.SELECT
            }
          );

          // Get harvest data for revenue estimation
          const harvestResult = await sequelize.query(
            `SELECT h.*, c.crop_name, f.name as field_name
             FROM harvests h
             JOIN crops c ON h.crop_id = c.id
             JOIN fields f ON h.field_id = f.id
             WHERE h.farm_id = $1
             ORDER BY h.actual_date DESC
             LIMIT 20`,
            { 
              replacements: [farmId],
              type: sequelize.QueryTypes.SELECT
            }
          );

          // Aggregate expenses by category
          const expensesByCategory = {};
          if (expensesResult.length > 0) {
            expensesResult.forEach(expense => {
              if (!expensesByCategory[expense.category]) {
                expensesByCategory[expense.category] = 0;
              }
              expensesByCategory[expense.category] += parseFloat(expense.amount);
            });
          }

          // Create a prompt for generating financial recommendations
          const prompt = `Generate 5 detailed financial optimization recommendations for a farm with the following details:
- Farm name: ${farmInfo.name}
- Farm type: ${farmInfo.farm_type}
- Location: ${farmInfo.location}
${Object.keys(expensesByCategory).length > 0 ? `- Expense categories and totals: ${JSON.stringify(expensesByCategory)}` : ''}
${expensesResult.length > 0 ? `- Recent expenses: ${JSON.stringify(expensesResult.slice(0, 5))}` : ''}
${harvestResult.length > 0 ? `- Recent harvests: ${JSON.stringify(harvestResult.slice(0, 5))}` : ''}

For each recommendation, provide:
1. Category (e.g., Cost Reduction, Revenue Increase, Tax Optimization, Investment, Operational Efficiency)
2. Detailed recommendation (2-3 sentences)
3. Potential financial impact (e.g., "Potential savings of 5-10% on input costs", "Estimated revenue increase of $X per acre")
4. Implementation difficulty (Easy, Moderate, Complex)
5. A confidence score (a number between 70 and 95)

Format your response as a JSON array with objects containing these fields:
[
  {
    "category": "...",
    "recommendation": "...",
    "financial_impact": "...",
    "implementation_difficulty": "...",
    "confidence_score": 85.5
  },
  ...
]`;

          // Call OpenAI API to generate recommendations
          const completion = await openai.chat.completions.create({
            model: "gpt-3.5-turbo",
            messages: [
              { role: "system", content: "You are an AI farming assistant that specializes in financial optimization for agricultural operations." },
              { role: "user", content: prompt }
            ],
            max_tokens: 1500,
            temperature: 0.7,
            response_format: { type: "json_object" }
          });

          // Parse the response to get recommendations
          const aiResponse = completion.choices[0].message.content.trim();
          try {
            const parsedResponse = JSON.parse(aiResponse);
            if (Array.isArray(parsedResponse) || (parsedResponse.recommendations && Array.isArray(parsedResponse.recommendations))) {
              const recommendations = Array.isArray(parsedResponse) ? parsedResponse : parsedResponse.recommendations;

              // Convert AI recommendations to our format
              financialRecommendations = recommendations.map(rec => ({
                category: rec.category,
                recommendation: rec.recommendation,
                financial_impact: rec.financial_impact,
                implementation_difficulty: rec.implementation_difficulty,
                confidence_score: parseFloat(rec.confidence_score) || Math.floor(Math.random() * 15) + 80, // Fallback to random score between 80-95
                is_implemented: false,
                created_at: new Date(),
                updated_at: new Date()
              }));
            }
          } catch (parseError) {
            console.error('Error parsing OpenAI response for financial recommendations:', parseError);
            // Will fall back to default recommendations below
          }
        } catch (aiError) {
          console.error('Error calling OpenAI API for financial recommendations:', aiError);
          // Will fall back to default recommendations below
        }
      }

      // If we couldn't generate AI recommendations, use default ones
      if (financialRecommendations.length === 0) {
        // Generate default recommendations
        financialRecommendations = [
          {
            category: 'Cost Reduction',
            recommendation: 'Implement bulk purchasing for fertilizers and chemicals by coordinating with neighboring farms. Negotiating volume discounts could significantly reduce input costs.',
            financial_impact: 'Potential savings of 10-15% on fertilizer and chemical expenses',
            implementation_difficulty: 'Moderate',
            confidence_score: 88.5,
            is_implemented: false,
            created_at: new Date(),
            updated_at: new Date()
          },
          {
            category: 'Revenue Increase',
            recommendation: 'Explore direct-to-consumer sales channels for a portion of your crop. Farmers markets and community-supported agriculture programs can increase profit margins by eliminating middlemen.',
            financial_impact: 'Potential revenue increase of 20-30% for marketed portion',
            implementation_difficulty: 'Complex',
            confidence_score: 82.3,
            is_implemented: false,
            created_at: new Date(),
            updated_at: new Date()
          },
          {
            category: 'Tax Optimization',
            recommendation: 'Review equipment depreciation schedules and consider Section 179 deductions for new equipment purchases. Consult with an agricultural tax specialist to maximize available tax benefits.',
            financial_impact: 'Potential tax savings of $5,000-$15,000 annually',
            implementation_difficulty: 'Moderate',
            confidence_score: 91.7,
            is_implemented: false,
            created_at: new Date(),
            updated_at: new Date()
          },
          {
            category: 'Operational Efficiency',
            recommendation: 'Implement precision agriculture technologies for variable rate application of inputs. GPS-guided systems can reduce overlap and optimize resource usage.',
            financial_impact: 'Estimated 7-12% reduction in input costs and 3-5% yield increase',
            implementation_difficulty: 'Complex',
            confidence_score: 86.2,
            is_implemented: false,
            created_at: new Date(),
            updated_at: new Date()
          },
          {
            category: 'Risk Management',
            recommendation: 'Evaluate crop insurance options and consider revenue protection policies. Recent weather patterns suggest increased volatility that could impact yield stability.',
            financial_impact: 'Protection against potential losses of 30-40% in adverse conditions',
            implementation_difficulty: 'Easy',
            confidence_score: 89.4,
            is_implemented: false,
            created_at: new Date(),
            updated_at: new Date()
          }
        ];
      }

      // Insert recommendations
      for (const rec of financialRecommendations) {
        await sequelize.query(
          `INSERT INTO ai_financial_recommendations 
           (farm_id, category, recommendation, financial_impact, implementation_difficulty, confidence_score, is_implemented, created_at, updated_at) 
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
          { 
            replacements: [
              farmId, 
              rec.category, 
              rec.recommendation, 
              rec.financial_impact, 
              rec.implementation_difficulty, 
              rec.confidence_score, 
              rec.is_implemented, 
              rec.created_at, 
              rec.updated_at
            ] 
          }
        );
      }

      // Fetch the newly inserted recommendations
      const newResult = await sequelize.query(
        'SELECT * FROM ai_financial_recommendations WHERE farm_id = $1 ORDER BY created_at DESC',
        { 
          replacements: [farmId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      return res.status(200).json({ recommendations: newResult });
    }

    return res.status(200).json({ recommendations: result });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Update an AI financial recommendation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const updateFinancialRecommendation = async (req, res) => {
  try {
    const id = req.params.id;
    const { isImplemented } = req.body;
    const now = new Date();

    const result = await sequelize.query(
      `UPDATE ai_financial_recommendations 
       SET is_implemented = $1, updated_at = $2
       WHERE id = $3 RETURNING *`,
      { 
        replacements: [isImplemented, now, id],
        type: sequelize.QueryTypes.UPDATE
      }
    );

    if (result[1] === 0) {
      return res.status(404).json({ message: 'Recommendation not found' });
    }

    return res.status(200).json({ recommendation: result[0][0] });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Get AI yield and profit maximization recommendations for a farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getYieldProfitRecommendations = async (req, res) => {
  try {
    const farmId = req.params.farmId;

    const result = await sequelize.query(
      'SELECT * FROM ai_yield_profit_recommendations WHERE farm_id = $1 ORDER BY created_at DESC',
      { 
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    // If no recommendations exist yet, generate new ones
    if (result.length === 0) {
      let yieldProfitRecommendations = [];

      // Try to generate AI-powered recommendations if OpenAI API key is configured and not a placeholder
      if (process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'REPLACE_WITH_REAL_API_KEY') {
        try {
          // Get farm information
          const farmResult = await sequelize.query(
            'SELECT name, farm_type, location FROM farms WHERE id = $1',
            { 
              replacements: [farmId],
              type: sequelize.QueryTypes.SELECT
            }
          );

          const farmInfo = farmResult.length > 0 ? farmResult[0] : { name: 'Unknown', farm_type: 'Unknown', location: 'Unknown' };

          // Get fields data
          const fieldsResult = await sequelize.query(
            'SELECT id, name, crop_type, size, size_unit, status FROM fields WHERE farm_id = $1',
            { 
              replacements: [farmId],
              type: sequelize.QueryTypes.SELECT
            }
          );

          // Get crops data
          const cropsResult = await sequelize.query(
            'SELECT id, crop_name, planting_date, expected_harvest_date FROM crops WHERE farm_id = $1',
            { 
              replacements: [farmId],
              type: sequelize.QueryTypes.SELECT
            }
          );

          // Get harvest data
          const harvestResult = await sequelize.query(
            `SELECT h.*, c.crop_name, f.name as field_name
             FROM harvests h
             JOIN crops c ON h.crop_id = c.id
             JOIN fields f ON h.field_id = f.id
             WHERE h.farm_id = $1
             ORDER BY h.actual_date DESC
             LIMIT 20`,
            { 
              replacements: [farmId],
              type: sequelize.QueryTypes.SELECT
            }
          );

          // Get soil test results
          const soilTestsResult = await sequelize.query(
            `SELECT str.*, ss.field_id, f.name as field_name 
             FROM soil_test_results str
             JOIN soil_samples ss ON str.soil_sample_id = ss.id
             JOIN fields f ON ss.field_id = f.id
             WHERE ss.farm_id = $1
             ORDER BY str.created_at DESC
             LIMIT 20`,
            { 
              replacements: [farmId],
              type: sequelize.QueryTypes.SELECT
            }
          );

          // Get weather data if available
          const weatherResult = await sequelize.query(
            `SELECT w.* FROM weather w
             JOIN fields f ON w.field_id = f.id
             WHERE f.farm_id = $1
             ORDER BY w.date DESC
             LIMIT 10`,
            { 
              replacements: [farmId],
              type: sequelize.QueryTypes.SELECT
            }
          );

          // Create a prompt for generating yield and profit maximization recommendations
          const prompt = `Generate 5 detailed yield and profit maximization recommendations for a farm with the following details:
- Farm name: ${farmInfo.name}
- Farm type: ${farmInfo.farm_type}
- Location: ${farmInfo.location}
- Fields: ${fieldsResult.map(f => `${f.name} (${f.crop_type}, ${f.size} ${f.size_unit}, status: ${f.status})`).join(', ')}
- Crops: ${cropsResult.map(c => `${c.crop_name} (planted: ${c.planting_date}, expected harvest: ${c.expected_harvest_date})`).join(', ')}
${harvestResult.length > 0 ? `- Recent harvests: ${JSON.stringify(harvestResult.slice(0, 3))}` : ''}
${soilTestsResult.length > 0 ? `- Soil test results: ${JSON.stringify(soilTestsResult.slice(0, 3))}` : ''}
${weatherResult.length > 0 ? `- Recent weather: ${JSON.stringify(weatherResult.slice(0, 3))}` : ''}

For each recommendation, provide:
1. Category (e.g., Crop Selection, Precision Agriculture, Pest Management, Irrigation Optimization, Harvest Timing)
2. Detailed recommendation (2-3 sentences)
3. Expected yield impact (e.g., "Potential yield increase of 10-15%")
4. Expected profit impact (e.g., "Estimated profit increase of $X per acre")
5. Implementation timeframe (e.g., "Immediate", "Next growing season", "Long-term strategy")
6. A confidence score (a number between 70 and 95)

Format your response as a JSON array with objects containing these fields:
[
  {
    "category": "...",
    "recommendation": "...",
    "yield_impact": "...",
    "profit_impact": "...",
    "implementation_timeframe": "...",
    "confidence_score": 85.5
  },
  ...
]`;

          // Call OpenAI API to generate recommendations
          const completion = await openai.chat.completions.create({
            model: "gpt-3.5-turbo",
            messages: [
              { role: "system", content: "You are an AI farming assistant that specializes in yield and profit maximization strategies for agricultural operations." },
              { role: "user", content: prompt }
            ],
            max_tokens: 1500,
            temperature: 0.7,
            response_format: { type: "json_object" }
          });

          // Parse the response to get recommendations
          const aiResponse = completion.choices[0].message.content.trim();
          try {
            const parsedResponse = JSON.parse(aiResponse);
            if (Array.isArray(parsedResponse) || (parsedResponse.recommendations && Array.isArray(parsedResponse.recommendations))) {
              const recommendations = Array.isArray(parsedResponse) ? parsedResponse : parsedResponse.recommendations;

              // Convert AI recommendations to our format
              yieldProfitRecommendations = recommendations.map(rec => ({
                category: rec.category,
                recommendation: rec.recommendation,
                yield_impact: rec.yield_impact,
                profit_impact: rec.profit_impact,
                implementation_timeframe: rec.implementation_timeframe,
                confidence_score: parseFloat(rec.confidence_score) || Math.floor(Math.random() * 15) + 80, // Fallback to random score between 80-95
                is_implemented: false,
                created_at: new Date(),
                updated_at: new Date()
              }));
            }
          } catch (parseError) {
            console.error('Error parsing OpenAI response for yield/profit recommendations:', parseError);
            // Will fall back to default recommendations below
          }
        } catch (aiError) {
          console.error('Error calling OpenAI API for yield/profit recommendations:', aiError);
          // Will fall back to default recommendations below
        }
      }

      // If we couldn't generate AI recommendations, use default ones
      if (yieldProfitRecommendations.length === 0) {
        // Generate default recommendations
        yieldProfitRecommendations = [
          {
            category: 'Precision Agriculture',
            recommendation: 'Implement variable rate technology for fertilizer application based on soil test results and yield maps. This targeted approach ensures nutrients are applied where needed most, reducing waste and optimizing plant growth.',
            yield_impact: 'Potential yield increase of 7-12%',
            profit_impact: 'Estimated profit increase of $50-75 per acre through input optimization and yield gains',
            implementation_timeframe: 'Next growing season',
            confidence_score: 89.5,
            is_implemented: false,
            created_at: new Date(),
            updated_at: new Date()
          },
          {
            category: 'Crop Selection',
            recommendation: 'Transition 20% of acreage to high-value specialty crops with strong market demand. Based on your soil types and climate, consider adding specialty grains or oilseeds that command premium prices.',
            yield_impact: 'Yield comparison not applicable due to crop change',
            profit_impact: 'Potential profit increase of 25-40% on transitioned acreage',
            implementation_timeframe: 'Next planting season',
            confidence_score: 82.3,
            is_implemented: false,
            created_at: new Date(),
            updated_at: new Date()
          },
          {
            category: 'Irrigation Optimization',
            recommendation: 'Install soil moisture sensors and implement deficit irrigation strategies during non-critical growth stages. This approach conserves water while maintaining optimal soil moisture during yield-determining growth phases.',
            yield_impact: 'Yield maintenance or increase of 3-5% during water-stressed seasons',
            profit_impact: 'Estimated profit increase of $30-45 per acre through water savings and yield protection',
            implementation_timeframe: 'Current growing season',
            confidence_score: 91.7,
            is_implemented: false,
            created_at: new Date(),
            updated_at: new Date()
          },
          {
            category: 'Pest Management',
            recommendation: 'Implement integrated pest management (IPM) with regular scouting and economic threshold-based treatments. This approach reduces unnecessary pesticide applications while maintaining effective pest control.',
            yield_impact: 'Yield protection of 10-15% compared to reactive pest management',
            profit_impact: 'Estimated profit increase of $40-60 per acre through reduced input costs and yield protection',
            implementation_timeframe: 'Immediate',
            confidence_score: 87.2,
            is_implemented: false,
            created_at: new Date(),
            updated_at: new Date()
          },
          {
            category: 'Soil Health',
            recommendation: 'Implement a comprehensive cover crop program to improve soil organic matter and reduce erosion. Select cover crop species that address specific soil constraints identified in your soil tests.',
            yield_impact: 'Gradual yield increase of 5-8% over 3-5 years',
            profit_impact: 'Long-term profit increase of $25-40 per acre through improved soil fertility and reduced input needs',
            implementation_timeframe: 'After current crop harvest',
            confidence_score: 93.4,
            is_implemented: false,
            created_at: new Date(),
            updated_at: new Date()
          }
        ];
      }

      // Insert recommendations
      for (const rec of yieldProfitRecommendations) {
        await sequelize.query(
          `INSERT INTO ai_yield_profit_recommendations 
           (farm_id, category, recommendation, yield_impact, profit_impact, implementation_timeframe, confidence_score, is_implemented, created_at, updated_at) 
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
          { 
            replacements: [
              farmId, 
              rec.category, 
              rec.recommendation, 
              rec.yield_impact, 
              rec.profit_impact, 
              rec.implementation_timeframe, 
              rec.confidence_score, 
              rec.is_implemented, 
              rec.created_at, 
              rec.updated_at
            ] 
          }
        );
      }

      // Fetch the newly inserted recommendations
      const newResult = await sequelize.query(
        'SELECT * FROM ai_yield_profit_recommendations WHERE farm_id = $1 ORDER BY created_at DESC',
        { 
          replacements: [farmId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      return res.status(200).json({ recommendations: newResult });
    }

    return res.status(200).json({ recommendations: result });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Update an AI yield and profit maximization recommendation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const updateYieldProfitRecommendation = async (req, res) => {
  try {
    const id = req.params.id;
    const { isImplemented } = req.body;
    const now = new Date();

    const result = await sequelize.query(
      `UPDATE ai_yield_profit_recommendations 
       SET is_implemented = $1, updated_at = $2
       WHERE id = $3 RETURNING *`,
      { 
        replacements: [isImplemented, now, id],
        type: sequelize.QueryTypes.UPDATE
      }
    );

    if (result[1] === 0) {
      return res.status(404).json({ message: 'Recommendation not found' });
    }

    return res.status(200).json({ recommendation: result[0][0] });
  } catch (error) {
    return handleServerError(res, error);
  }
};
