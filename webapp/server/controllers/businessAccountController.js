import User from '../models/User.js';
import Supplier from '../models/Supplier.js';
import { sequelize } from '../config/database.js';

// Get business account information
export const getBusinessAccount = async (req, res) => {
  try {
    const userId = req.user.id;

    // Find user
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if user is a business owner
    if (!user.is_business_owner) {
      // Return empty business data instead of an error
      return res.status(200).json({ businessAccount: null });
    }

    let businessData = {
      id: user.id,
      email: user.email,
      userType: user.user_type,
      isApproved: user.is_approved
    };

    // Get business-specific data based on user type
    if (user.user_type === 'supplier') {
      const supplier = await Supplier.findOne({
        where: { user_id: userId }
      });

      if (supplier) {
        businessData = {
          ...businessData,
          name: supplier.name,
          contactName: supplier.contact_name,
          phoneNumber: supplier.phone,
          address: supplier.address,
          website: supplier.website,
          description: supplier.description,
          businessHours: supplier.business_hours,
          isActive: supplier.is_active
        };
      }
    } else if (user.user_type === 'vet') {
      try {
        const Vet = require('../models/Vet.js').default;
        const vet = await Vet.findOne({
          where: { user_id: userId }
        });

        if (vet) {
          businessData = {
            ...businessData,
            name: vet.name,
            contactName: vet.contact_name,
            phoneNumber: vet.phone,
            address: vet.address,
            website: vet.website,
            description: vet.description,
            isActive: vet.is_active
          };
        }
      } catch (error) {
        console.error('Error loading vet data:', error);
      }
    }

    return res.status(200).json({ businessAccount: businessData });
  } catch (error) {
    console.error('Error getting business account:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update business account information
export const updateBusinessAccount = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const userId = req.user.id;
    const { 
      name, 
      contactName, 
      phoneNumber, 
      address, 
      website, 
      description, 
      businessHours 
    } = req.body;

    // Find user
    const user = await User.findByPk(userId);
    if (!user) {
      await transaction.rollback();
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if user is a business owner
    if (!user.is_business_owner) {
      // If not a business owner, create a new business account
      user.is_business_owner = true;
      await user.save({ transaction });
    }

    let businessData = {
      id: user.id,
      email: user.email,
      userType: user.user_type,
      isApproved: user.is_approved
    };

    // Update business-specific data based on user type
    if (user.user_type === 'supplier') {
      const supplier = await Supplier.findOne({
        where: { user_id: userId }
      });

      if (supplier) {
        // Update supplier
        await supplier.update({
          name: name || supplier.name,
          contact_name: contactName || supplier.contact_name,
          phone: phoneNumber || supplier.phone,
          address: address !== undefined ? address : supplier.address,
          website: website !== undefined ? website : supplier.website,
          description: description !== undefined ? description : supplier.description,
          business_hours: businessHours !== undefined ? businessHours : supplier.business_hours
        }, { transaction });

        businessData = {
          ...businessData,
          name: supplier.name,
          contactName: supplier.contact_name,
          phoneNumber: supplier.phone,
          address: supplier.address,
          website: supplier.website,
          description: supplier.description,
          businessHours: supplier.business_hours,
          isActive: supplier.is_active
        };
      } else {
        // Create supplier if it doesn't exist
        const newSupplier = await Supplier.create({
          user_id: userId,
          name: name || `${user.first_name} ${user.last_name}'s Business`,
          contact_name: contactName || `${user.first_name} ${user.last_name}`,
          phone: phoneNumber || user.phone_number,
          address: address,
          website: website,
          description: description,
          business_hours: businessHours,
          is_active: true
        }, { transaction });

        businessData = {
          ...businessData,
          name: newSupplier.name,
          contactName: newSupplier.contact_name,
          phoneNumber: newSupplier.phone,
          address: newSupplier.address,
          website: newSupplier.website,
          description: newSupplier.description,
          businessHours: newSupplier.business_hours,
          isActive: newSupplier.is_active
        };
      }
    } else if (user.user_type === 'vet') {
      try {
        const Vet = require('../models/Vet.js').default;
        const vet = await Vet.findOne({
          where: { user_id: userId }
        });

        if (vet) {
          // Update vet
          await vet.update({
            name: name || vet.name,
            contact_name: contactName || vet.contact_name,
            phone: phoneNumber || vet.phone,
            address: address !== undefined ? address : vet.address,
            website: website !== undefined ? website : vet.website,
            description: description !== undefined ? description : vet.description
          }, { transaction });

          businessData = {
            ...businessData,
            name: vet.name,
            contactName: vet.contact_name,
            phoneNumber: vet.phone,
            address: vet.address,
            website: vet.website,
            description: vet.description,
            isActive: vet.is_active
          };
        } else {
          // Create vet if it doesn't exist
          const newVet = await Vet.create({
            user_id: userId,
            name: name || `${user.first_name} ${user.last_name}'s Veterinary Practice`,
            contact_name: contactName || `${user.first_name} ${user.last_name}`,
            phone: phoneNumber || user.phone_number,
            address: address,
            website: website,
            description: description,
            is_active: true
          }, { transaction });

          businessData = {
            ...businessData,
            name: newVet.name,
            contactName: newVet.contact_name,
            phoneNumber: newVet.phone,
            address: newVet.address,
            website: newVet.website,
            description: newVet.description,
            isActive: newVet.is_active
          };
        }
      } catch (error) {
        console.error('Error updating vet data:', error);
        await transaction.rollback();
        return res.status(500).json({ error: 'Error updating vet data' });
      }
    }

    await transaction.commit();
    return res.status(200).json({ 
      message: 'Business account updated successfully',
      businessAccount: businessData 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating business account:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create business account
export const createBusinessAccount = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const userId = req.user.id;
    const { 
      userType,
      name, 
      contactName, 
      phoneNumber, 
      address, 
      website, 
      description, 
      businessHours 
    } = req.body;

    // Validate user type
    if (userType !== 'supplier' && userType !== 'vet') {
      await transaction.rollback();
      return res.status(400).json({ error: 'Invalid user type. Must be supplier or vet' });
    }

    // Find user
    const user = await User.findByPk(userId);
    if (!user) {
      await transaction.rollback();
      return res.status(404).json({ error: 'User not found' });
    }

    // Update user fields
    user.user_type = userType;
    user.is_business_owner = true;
    user.is_approved = false; // Business accounts need approval
    await user.save({ transaction });

    let businessData = {
      id: user.id,
      email: user.email,
      userType: user.user_type,
      isApproved: user.is_approved
    };

    // Create business-specific data based on user type
    if (userType === 'supplier') {
      // Create supplier
      const newSupplier = await Supplier.create({
        user_id: userId,
        name: name || `${user.first_name} ${user.last_name}'s Business`,
        contact_name: contactName || `${user.first_name} ${user.last_name}`,
        phone: phoneNumber || user.phone_number,
        address: address,
        website: website,
        description: description,
        business_hours: businessHours,
        is_active: true
      }, { transaction });

      businessData = {
        ...businessData,
        name: newSupplier.name,
        contactName: newSupplier.contact_name,
        phoneNumber: newSupplier.phone,
        address: newSupplier.address,
        website: newSupplier.website,
        description: newSupplier.description,
        businessHours: newSupplier.business_hours,
        isActive: newSupplier.is_active
      };
    } else if (userType === 'vet') {
      try {
        const Vet = require('../models/Vet.js').default;

        // Create vet
        const newVet = await Vet.create({
          user_id: userId,
          name: name || `${user.first_name} ${user.last_name}'s Veterinary Practice`,
          contact_name: contactName || `${user.first_name} ${user.last_name}`,
          phone: phoneNumber || user.phone_number,
          address: address,
          website: website,
          description: description,
          is_active: true
        }, { transaction });

        businessData = {
          ...businessData,
          name: newVet.name,
          contactName: newVet.contact_name,
          phoneNumber: newVet.phone,
          address: newVet.address,
          website: newVet.website,
          description: newVet.description,
          isActive: newVet.is_active
        };
      } catch (error) {
        console.error('Error creating vet data:', error);
        await transaction.rollback();
        return res.status(500).json({ error: 'Error creating vet data' });
      }
    }

    await transaction.commit();
    return res.status(201).json({ 
      message: 'Business account created successfully',
      businessAccount: businessData 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating business account:', error);
    return res.status(500).json({ error: error.message });
  }
};
