import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import all models
import '../models/index.js';

/**
 * Compare database schema with expected schema
 * This function checks the current database schema against the expected schema
 * defined by the Sequelize models and migration files
 */
export const checkDatabaseHealth = async (req, res) => {
  try {
    console.log('Checking database health...');

    // Get the schema from environment variables or use default
    const schema = process.env.DB_SCHEMA || 'site';
    console.log(`Using schema: ${schema}`);

    // Get all models from sequelize
    const models = Object.values(sequelize.models);

    // Results object to store comparison results
    const results = {
      missingTables: [],
      missingColumns: [],
      typeMismatches: [],
      missingForeignKeys: [],
      totalIssues: 0
    };

    // Process each model
    for (const model of models) {
      const modelName = model.name;
      const tableName = model.tableName;

      // Get model file path - this helps identify where in the code the model is defined
      const modelFilePath = `webapp/server/models/${modelName}.js`;

      console.log(`\nProcessing model: ${modelName} (table: ${tableName})`);

      // Get model attributes
      const modelAttributes = Object.entries(model.rawAttributes).map(([name, attribute]) => {
        // Extract more detailed type information
        let typeInfo = {
          name: attribute.type.constructor.name,
          options: {}
        };

        // Handle DECIMAL type with precision and scale
        if (attribute.type.constructor.name === 'DECIMAL' && attribute.type._precision !== undefined) {
          typeInfo.options.precision = attribute.type._precision;
          typeInfo.options.scale = attribute.type._scale !== undefined ? attribute.type._scale : 0;
        }

        // Handle STRING type with length
        if (attribute.type.constructor.name === 'STRING' && attribute.type.options && attribute.type.options.length) {
          typeInfo.options.length = attribute.type.options.length;
        }

        // Handle ARRAY type with item type
        if (attribute.type.constructor.name === 'ARRAY' && attribute.type.options && attribute.type.options.type) {
          typeInfo.options.itemType = attribute.type.options.type.constructor.name;
        }

        return {
          name,
          type: typeInfo,
          allowNull: attribute.allowNull !== false, // Default to true if not specified
          defaultValue: attribute.defaultValue,
          primaryKey: attribute.primaryKey || false,
          references: attribute.references ? {
            model: attribute.references.model,
            key: attribute.references.key
          } : null,
          comment: attribute.comment
        };
      });

      // Query database schema for this table
      const tableExistsQuery = `
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = '${schema}'
          AND table_name = '${tableName}'
        );
      `;

      const [tableExistsResult] = await sequelize.query(tableExistsQuery);
      const tableExists = tableExistsResult[0].exists;

      if (!tableExists) {
        console.log(`Table ${tableName} does not exist in the database.`);
        results.missingTables.push({
          modelName,
          tableName,
          attributes: modelAttributes,
          modelFilePath
        });
        results.totalIssues++;
        continue;
      }

      // Query database schema for columns in this table
      const columnsQuery = `
        SELECT 
          column_name, 
          data_type, 
          udt_name,
          is_nullable,
          column_default,
          is_identity
        FROM 
          information_schema.columns
        WHERE 
          table_schema = '${schema}'
          AND table_name = '${tableName}';
      `;

      const [dbColumns] = await sequelize.query(columnsQuery);
      console.log(`Found ${dbColumns.length} columns in database table ${tableName}`);

      // Check for missing columns
      for (const attr of modelAttributes) {
        const dbColumn = dbColumns.find(col => col.column_name === attr.name);

        if (!dbColumn) {
          console.log(`Column ${attr.name} is missing from database table ${tableName}`);
          results.missingColumns.push({
            tableName,
            columnName: attr.name,
            expectedType: attr.type.name,
            modelAttribute: attr,
            modelFilePath
          });
          results.totalIssues++;
        } else {
          // Check for type mismatches
          const dbType = dbColumn.data_type.toUpperCase();
          let expectedType = mapSequelizeTypeToPostgres(attr.type.name, attr.type.options);

          // For USER-DEFINED types (like ENUMs), pass the udt_name for more specific matching
          const udtName = dbType === 'USER-DEFINED' ? dbColumn.udt_name : null;

          if (!typesMatch(dbType, expectedType, udtName)) {
            console.log(`Type mismatch for column ${attr.name} in table ${tableName}: expected ${expectedType}, got ${dbType}`);
            results.typeMismatches.push({
              tableName,
              columnName: attr.name,
              expectedType,
              actualType: dbType,
              modelAttribute: attr,
              modelFilePath
            });
            results.totalIssues++;
          }
        }
      }

      // Check for foreign keys
      if (model.associations) {
        const foreignKeysQuery = `
          SELECT
            tc.constraint_name,
            kcu.column_name,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name
          FROM
            information_schema.table_constraints AS tc
            JOIN information_schema.key_column_usage AS kcu
              ON tc.constraint_name = kcu.constraint_name
              AND tc.table_schema = kcu.table_schema
            JOIN information_schema.constraint_column_usage AS ccu
              ON ccu.constraint_name = tc.constraint_name
              AND ccu.table_schema = tc.table_schema
          WHERE
            tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_schema = '${schema}'
            AND tc.table_name = '${tableName}';
        `;

        const [dbForeignKeys] = await sequelize.query(foreignKeysQuery);

        // Check each association
        for (const [associationName, association] of Object.entries(model.associations)) {
          if (association.associationType === 'BelongsTo') {
            const foreignKey = association.foreignKey;
            const targetTable = association.target.tableName;

            const dbForeignKey = dbForeignKeys.find(fk => 
              fk.column_name === foreignKey && 
              fk.foreign_table_name === targetTable
            );

            if (!dbForeignKey) {
              console.log(`Foreign key ${foreignKey} to table ${targetTable} is missing from database table ${tableName}`);
              results.missingForeignKeys.push({
                tableName,
                foreignKey,
                targetTable,
                associationType: association.associationType,
                associationName
              });
              results.totalIssues++;
            }
          }
        }
      }
    }

    // Return the results
    return res.status(200).json({
      success: true,
      results
    });
  } catch (error) {
    console.error('Error checking database health:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to check database health',
      details: error.message
    });
  }
};

/**
 * Map Sequelize type to PostgreSQL type
 */
function mapSequelizeTypeToPostgres(typeName, options = {}) {
  switch (typeName) {
    case 'UUID':
      return 'UUID';
    case 'STRING':
      const length = options.length || 255;
      return `VARCHAR(${length})`;
    case 'TEXT':
      return 'TEXT';
    case 'BOOLEAN':
      return 'BOOLEAN';
    case 'INTEGER':
      return 'INTEGER';
    case 'BIGINT':
      return 'BIGINT';
    case 'FLOAT':
      return 'REAL';
    case 'REAL':
      return 'REAL';
    case 'DOUBLE':
      return 'DOUBLE PRECISION';
    case 'DECIMAL':
      const precision = options.precision || 15;
      const scale = options.scale || 2;
      return `NUMERIC(${precision},${scale})`;
    case 'DATE':
      return 'TIMESTAMP WITH TIME ZONE';
    case 'DATEONLY':
      return 'DATE';
    case 'TIME':
      return 'TIME';
    case 'JSONB':
      return 'JSONB';
    case 'JSON':
      return 'JSON';
    case 'ARRAY':
      return 'ARRAY';
    case 'ENUM':
      return 'VARCHAR';
    default:
      return 'VARCHAR';
  }
}

/**
 * Check if database type matches expected type
 * @param {string} dbType - The database type
 * @param {string} expectedType - The expected type from the model
 * @param {string|null} udtName - The user-defined type name (for USER-DEFINED types)
 * @returns {boolean} - Whether the types match
 */
function typesMatch(dbType, expectedType, udtName = null) {
  // Normalize types for comparison
  dbType = dbType.toUpperCase();
  expectedType = expectedType.toUpperCase();

  // Direct match
  if (dbType === expectedType) return true;

  // Handle common type aliases
  if (dbType === 'CHARACTER VARYING' && expectedType.startsWith('VARCHAR')) return true;
  if (dbType === 'NUMERIC' && (expectedType.startsWith('DECIMAL') || expectedType.startsWith('NUMERIC'))) return true;
  if (dbType === 'CHARACTER VARYING' && (expectedType.startsWith('NUMERIC') || expectedType.startsWith('DECIMAL'))) return true;
  if (dbType === 'CHARACTER VARYING' && (expectedType === 'TIMESTAMP WITH TIME ZONE' || expectedType === 'TIMESTAMP' || expectedType === 'DATE')) return true;
  if (dbType === 'TIMESTAMP WITHOUT TIME ZONE' && (expectedType === 'TIMESTAMP' || expectedType === 'TIMESTAMP WITH TIME ZONE')) return true;
  if (dbType === 'TIMESTAMP WITH TIME ZONE' && (expectedType === 'TIMESTAMP' || expectedType === 'TIMESTAMP WITHOUT TIME ZONE' || expectedType === 'DATE')) return true;
  if (dbType === 'DOUBLE PRECISION' && (expectedType === 'FLOAT' || expectedType === 'REAL' || expectedType === 'DOUBLE')) return true;
  if (dbType === 'REAL' && (expectedType === 'FLOAT' || expectedType === 'DOUBLE PRECISION')) return true;
  if (dbType === 'INTEGER' && (expectedType === 'INT' || expectedType === 'SERIAL')) return true;
  if (dbType === 'BIGINT' && (expectedType === 'INT8' || expectedType === 'BIGSERIAL')) return true;
  if (dbType === 'SMALLINT' && (expectedType === 'INT2' || expectedType === 'SMALLSERIAL')) return true;
  if (dbType === 'BOOLEAN' && expectedType === 'BOOL') return true;
  if (dbType === 'CHARACTER' && (expectedType === 'CHAR' || expectedType.startsWith('CHARACTER'))) return true;
  if (dbType === 'TEXT' && (expectedType === 'VARCHAR' || expectedType === 'CHARACTER VARYING')) return true;

  // Handle USER-DEFINED types (like ENUMs)
  if (dbType === 'USER-DEFINED') {
    // If it's an ENUM type, it's a match
    if (expectedType === 'ENUM') return true;

    // If we have the udt_name, we can do more specific matching
    if (udtName) {
      // For custom enum types, the udt_name will be the enum type name
      // Check if the expected type contains the enum type name
      // This handles cases where the model defines an ENUM type with specific values
      if (expectedType.includes(udtName.toUpperCase())) return true;

      // Check if the udt_name matches a known enum type pattern
      // For example, if the udt_name is "enum_users_user_type", it's likely an enum for the user_type column
      if (udtName.startsWith('enum_')) return true;
    }
  }

  // Handle parameterized types (ignore parameters for comparison)
  if (dbType.includes('(') && expectedType.includes('(')) {
    const dbBaseType = dbType.split('(')[0].trim();
    const expectedBaseType = expectedType.split('(')[0].trim();
    if (dbBaseType === expectedBaseType) return true;

    // Check for common base type equivalences
    if (dbBaseType === 'CHARACTER VARYING' && expectedBaseType === 'VARCHAR') return true;
    if (dbBaseType === 'NUMERIC' && (expectedBaseType === 'DECIMAL' || expectedBaseType === 'NUMERIC')) return true;
  }

  // If none of the conditions match, the types don't match
  return false;
}

/**
 * Get all migration files and their status
 */
export const getMigrationFiles = async (req, res) => {
  try {
    const dbDir = path.join(process.cwd(), 'server', 'db');
    const migrationsDir = path.join(dbDir, 'migrations');

    // Check if the migrations directory exists
    if (!fs.existsSync(migrationsDir)) {
      return res.status(404).json({
        success: false,
        error: 'Migrations directory not found'
      });
    }

    // Get all SQL files in the migrations directory
    const files = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .map(file => ({
        name: file,
        path: path.join(migrationsDir, file),
        content: fs.readFileSync(path.join(migrationsDir, file), 'utf8').substring(0, 500) // Get first 500 chars for preview
      }));

    return res.status(200).json({
      success: true,
      files
    });
  } catch (error) {
    console.error('Error getting migration files:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to get migration files',
      details: error.message
    });
  }
};

/**
 * Fix a specific schema issue
 */
export const fixSchemaIssue = async (req, res) => {
  try {
    const { issueType, issue } = req.body;

    if (!issueType || !issue) {
      return res.status(400).json({
        success: false,
        error: 'Issue type and issue details are required'
      });
    }

    // Get the schema from environment variables or use default
    const schema = process.env.DB_SCHEMA || 'site';
    console.log(`Using schema: ${schema}`);

    let sql = '';
    let message = '';

    switch (issueType) {
      case 'missingTable':
        // Creating a table would require a complex SQL statement based on model attributes
        // For simplicity, we'll return a message suggesting to use migrations
        return res.status(400).json({
          success: false,
          error: 'Creating missing tables is not supported through this interface. Please use migrations instead.'
        });

      case 'missingColumn':
        const { tableName, columnName, expectedType, modelAttribute } = issue;
        const postgresType = mapSequelizeTypeToPostgres(expectedType, modelAttribute?.type?.options || {});
        const nullableClause = modelAttribute?.allowNull === false ? 'NOT NULL' : '';

        sql = `ALTER TABLE "${schema}"."${tableName}" ADD COLUMN "${columnName}" ${postgresType} ${nullableClause};`;
        message = `Added missing column ${columnName} to table ${tableName}`;
        break;

      case 'typeMismatch':
        const { tableName: tmTableName, columnName: tmColumnName, expectedType: tmExpectedType } = issue;
        const tmPostgresType = mapSequelizeTypeToPostgres(tmExpectedType, issue.modelAttribute?.type?.options || {});

        sql = `ALTER TABLE "${schema}"."${tmTableName}" ALTER COLUMN "${tmColumnName}" TYPE ${tmPostgresType} USING "${tmColumnName}"::${tmPostgresType};`;
        message = `Fixed type mismatch for column ${tmColumnName} in table ${tmTableName}`;
        break;

      case 'missingForeignKey':
        const { tableName: fkTableName, foreignKey, targetTable } = issue;

        // Generate a constraint name
        const constraintName = `fk_${fkTableName}_${foreignKey}`;

        sql = `ALTER TABLE "${schema}"."${fkTableName}" ADD CONSTRAINT "${constraintName}" FOREIGN KEY ("${foreignKey}") REFERENCES "${schema}"."${targetTable}" (id);`;
        message = `Added missing foreign key ${foreignKey} to table ${fkTableName} referencing ${targetTable}`;
        break;

      default:
        return res.status(400).json({
          success: false,
          error: `Unknown issue type: ${issueType}`
        });
    }

    // Execute the SQL
    if (sql) {
      await sequelize.query(sql);
    }

    return res.status(200).json({
      success: true,
      message,
      sql
    });
  } catch (error) {
    console.error('Error fixing schema issue:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fix schema issue',
      details: error.message
    });
  }
};

/**
 * Fix all schema issues
 */
export const fixAllSchemaIssues = async (req, res) => {
  try {
    // First, get all the issues
    const schema = process.env.DB_SCHEMA || 'site';
    console.log(`Using schema: ${schema}`);

    // Get all models from sequelize
    const models = Object.values(sequelize.models);

    // Results object to store comparison results
    const results = {
      missingTables: [],
      missingColumns: [],
      typeMismatches: [],
      missingForeignKeys: [],
      totalIssues: 0
    };

    // Process each model to find issues (similar to checkDatabaseHealth)
    // This is a simplified version that reuses the checkDatabaseHealth logic
    for (const model of models) {
      const modelName = model.name;
      const tableName = model.tableName;

      console.log(`\nProcessing model: ${modelName} (table: ${tableName})`);

      // Get model attributes
      const modelAttributes = Object.entries(model.rawAttributes).map(([name, attribute]) => {
        // Extract more detailed type information
        let typeInfo = {
          name: attribute.type.constructor.name,
          options: {}
        };

        // Handle DECIMAL type with precision and scale
        if (attribute.type.constructor.name === 'DECIMAL' && attribute.type._precision !== undefined) {
          typeInfo.options.precision = attribute.type._precision;
          typeInfo.options.scale = attribute.type._scale !== undefined ? attribute.type._scale : 0;
        }

        // Handle STRING type with length
        if (attribute.type.constructor.name === 'STRING' && attribute.type.options && attribute.type.options.length) {
          typeInfo.options.length = attribute.type.options.length;
        }

        // Handle ARRAY type with item type
        if (attribute.type.constructor.name === 'ARRAY' && attribute.type.options && attribute.type.options.type) {
          typeInfo.options.itemType = attribute.type.options.type.constructor.name;
        }

        return {
          name,
          type: typeInfo,
          allowNull: attribute.allowNull !== false, // Default to true if not specified
          defaultValue: attribute.defaultValue,
          primaryKey: attribute.primaryKey || false,
          references: attribute.references ? {
            model: attribute.references.model,
            key: attribute.references.key
          } : null,
          comment: attribute.comment
        };
      });

      // Query database schema for this table
      const tableExistsQuery = `
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = '${schema}'
          AND table_name = '${tableName}'
        );
      `;

      const [tableExistsResult] = await sequelize.query(tableExistsQuery);
      const tableExists = tableExistsResult[0].exists;

      if (!tableExists) {
        console.log(`Table ${tableName} does not exist in the database.`);
        results.missingTables.push({
          modelName,
          tableName,
          attributes: modelAttributes
        });
        results.totalIssues++;
        continue;
      }

      // Query database schema for columns in this table
      const columnsQuery = `
        SELECT 
          column_name, 
          data_type, 
          udt_name,
          is_nullable,
          column_default,
          is_identity
        FROM 
          information_schema.columns
        WHERE 
          table_schema = '${schema}'
          AND table_name = '${tableName}';
      `;

      const [dbColumns] = await sequelize.query(columnsQuery);
      console.log(`Found ${dbColumns.length} columns in database table ${tableName}`);

      // Check for missing columns
      for (const attr of modelAttributes) {
        const dbColumn = dbColumns.find(col => col.column_name === attr.name);

        if (!dbColumn) {
          console.log(`Column ${attr.name} is missing from database table ${tableName}`);
          results.missingColumns.push({
            tableName,
            columnName: attr.name,
            expectedType: attr.type.name,
            modelAttribute: attr
          });
          results.totalIssues++;
        } else {
          // Check for type mismatches
          const dbType = dbColumn.data_type.toUpperCase();
          let expectedType = mapSequelizeTypeToPostgres(attr.type.name, attr.type.options);

          // For USER-DEFINED types (like ENUMs), pass the udt_name for more specific matching
          const udtName = dbType === 'USER-DEFINED' ? dbColumn.udt_name : null;

          if (!typesMatch(dbType, expectedType, udtName)) {
            console.log(`Type mismatch for column ${attr.name} in table ${tableName}: expected ${expectedType}, got ${dbType}`);
            results.typeMismatches.push({
              tableName,
              columnName: attr.name,
              expectedType,
              actualType: dbType,
              modelAttribute: attr
            });
            results.totalIssues++;
          }
        }
      }

      // Check for foreign keys
      if (model.associations) {
        const foreignKeysQuery = `
          SELECT
            tc.constraint_name,
            kcu.column_name,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name
          FROM
            information_schema.table_constraints AS tc
            JOIN information_schema.key_column_usage AS kcu
              ON tc.constraint_name = kcu.constraint_name
              AND tc.table_schema = kcu.table_schema
            JOIN information_schema.constraint_column_usage AS ccu
              ON ccu.constraint_name = tc.constraint_name
              AND ccu.table_schema = tc.table_schema
          WHERE
            tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_schema = '${schema}'
            AND tc.table_name = '${tableName}';
        `;

        const [dbForeignKeys] = await sequelize.query(foreignKeysQuery);

        // Check each association
        for (const [associationName, association] of Object.entries(model.associations)) {
          if (association.associationType === 'BelongsTo') {
            const foreignKey = association.foreignKey;
            const targetTable = association.target.tableName;

            const dbForeignKey = dbForeignKeys.find(fk => 
              fk.column_name === foreignKey && 
              fk.foreign_table_name === targetTable
            );

            if (!dbForeignKey) {
              console.log(`Foreign key ${foreignKey} to table ${targetTable} is missing from database table ${tableName}`);
              results.missingForeignKeys.push({
                tableName,
                foreignKey,
                targetTable,
                associationType: association.associationType,
                associationName
              });
              results.totalIssues++;
            }
          }
        }
      }
    }

    // Now fix the issues
    const fixedIssues = [];
    const failedIssues = [];
    const skippedIssues = [];

    // Skip missing tables as they require complex handling
    if (results.missingTables.length > 0) {
      skippedIssues.push({
        type: 'missingTables',
        count: results.missingTables.length,
        reason: 'Creating missing tables is not supported through this interface. Please use migrations instead.'
      });
    }

    // Fix missing columns
    for (const issue of results.missingColumns) {
      try {
        const { tableName, columnName, expectedType, modelAttribute } = issue;
        const postgresType = mapSequelizeTypeToPostgres(expectedType, modelAttribute?.type?.options || {});
        const nullableClause = modelAttribute?.allowNull === false ? 'NOT NULL' : '';

        const sql = `ALTER TABLE "${schema}"."${tableName}" ADD COLUMN "${columnName}" ${postgresType} ${nullableClause};`;
        await sequelize.query(sql);

        fixedIssues.push({
          type: 'missingColumn',
          tableName,
          columnName,
          sql
        });
      } catch (error) {
        failedIssues.push({
          type: 'missingColumn',
          issue,
          error: error.message
        });
      }
    }

    // Fix type mismatches
    for (const issue of results.typeMismatches) {
      try {
        const { tableName, columnName, expectedType } = issue;
        const postgresType = mapSequelizeTypeToPostgres(expectedType, issue.modelAttribute?.type?.options || {});

        const sql = `ALTER TABLE "${schema}"."${tableName}" ALTER COLUMN "${columnName}" TYPE ${postgresType} USING "${columnName}"::${postgresType};`;
        await sequelize.query(sql);

        fixedIssues.push({
          type: 'typeMismatch',
          tableName,
          columnName,
          sql
        });
      } catch (error) {
        failedIssues.push({
          type: 'typeMismatch',
          issue,
          error: error.message
        });
      }
    }

    // Fix missing foreign keys
    for (const issue of results.missingForeignKeys) {
      try {
        const { tableName, foreignKey, targetTable } = issue;

        // Generate a constraint name
        const constraintName = `fk_${tableName}_${foreignKey}`;

        const sql = `ALTER TABLE "${schema}"."${tableName}" ADD CONSTRAINT "${constraintName}" FOREIGN KEY ("${foreignKey}") REFERENCES "${schema}"."${targetTable}" (id);`;
        await sequelize.query(sql);

        fixedIssues.push({
          type: 'missingForeignKey',
          tableName,
          foreignKey,
          targetTable,
          sql
        });
      } catch (error) {
        failedIssues.push({
          type: 'missingForeignKey',
          issue,
          error: error.message
        });
      }
    }

    return res.status(200).json({
      success: true,
      message: `Fixed ${fixedIssues.length} issues, ${failedIssues.length} failed, ${skippedIssues.length} skipped`,
      fixedIssues,
      failedIssues,
      skippedIssues
    });
  } catch (error) {
    console.error('Error fixing all schema issues:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fix all schema issues',
      details: error.message
    });
  }
};
