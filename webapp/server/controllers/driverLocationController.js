import DriverLocation from '../models/DriverLocation.js';
import Farm from '../models/Farm.js';
import Driver from '../models/Driver.js';
import { sequelize } from '../config/database.js';

// Create a new driver location
export const createDriverLocation = async (req, res) => {
  try {
    const { 
      farmId,
      driverId,
      latitude,
      longitude,
      accuracy,
      altitude,
      speed,
      heading,
      address,
      timestamp = new Date(),
      deviceId,
      batteryLevel
    } = req.body;

    // Validate required fields
    if (!farmId || !driverId || latitude === undefined || longitude === undefined) {
      return res.status(400).json({ 
        error: 'Farm ID, driver ID, latitude, and longitude are required' 
      });
    }

    // Check if farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if driver exists
    const driver = await Driver.findByPk(driverId);
    if (!driver) {
      return res.status(404).json({ error: 'Driver not found' });
    }

    // Create new driver location
    const driverLocation = await DriverLocation.create({
      farm_id: farmId,
      driver_id: driverId,
      latitude,
      longitude,
      accuracy: accuracy || null,
      altitude: altitude || null,
      speed: speed || null,
      heading: heading || null,
      address: address || null,
      timestamp,
      device_id: deviceId || null,
      battery_level: batteryLevel || null
    });

    return res.status(201).json({
      driverLocation: {
        id: driverLocation.id,
        farmId: driverLocation.farm_id,
        driverId: driverLocation.driver_id,
        latitude: driverLocation.latitude,
        longitude: driverLocation.longitude,
        accuracy: driverLocation.accuracy,
        altitude: driverLocation.altitude,
        speed: driverLocation.speed,
        heading: driverLocation.heading,
        address: driverLocation.address,
        timestamp: driverLocation.timestamp,
        deviceId: driverLocation.device_id,
        batteryLevel: driverLocation.battery_level,
        createdAt: driverLocation.created_at,
        updatedAt: driverLocation.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating driver location:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get driver locations with optional filtering
export const getDriverLocations = async (req, res) => {
  try {
    const { 
      farmId, 
      driverId,
      startDate,
      endDate,
      limit = 100,
      offset = 0
    } = req.query;

    // Build the where clause based on query parameters
    const whereClause = {};

    if (farmId) {
      whereClause.farm_id = farmId;
    }

    if (driverId) {
      whereClause.driver_id = driverId;
    }

    // Date range filtering for timestamp
    if (startDate || endDate) {
      whereClause.timestamp = {};

      if (startDate) {
        whereClause.timestamp[sequelize.Op.gte] = new Date(startDate);
      }

      if (endDate) {
        whereClause.timestamp[sequelize.Op.lte] = new Date(endDate);
      }
    }

    // Get driver locations with pagination
    const driverLocations = await DriverLocation.findAll({
      where: whereClause,
      include: [
        { model: Farm, as: 'driverLocationFarm', attributes: ['id', 'name'] },
        { model: Driver, as: 'locationDriver', attributes: ['id', 'first_name', 'last_name'] }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['timestamp', 'DESC']]
    });

    // Get total count for pagination
    const totalCount = await DriverLocation.count({ where: whereClause });

    return res.status(200).json({
      driverLocations: driverLocations.map(location => ({
        id: location.id,
        farmId: location.farm_id,
        driverId: location.driver_id,
        latitude: location.latitude,
        longitude: location.longitude,
        accuracy: location.accuracy,
        altitude: location.altitude,
        speed: location.speed,
        heading: location.heading,
        address: location.address,
        timestamp: location.timestamp,
        deviceId: location.device_id,
        batteryLevel: location.battery_level,
        farm: location.driverLocationFarm,
        driver: location.locationDriver,
        createdAt: location.created_at,
        updatedAt: location.updated_at
      })),
      totalCount
    });
  } catch (error) {
    console.error('Error getting driver locations:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get latest driver location by driver ID
export const getLatestDriverLocation = async (req, res) => {
  try {
    const { driverId } = req.params;

    // Check if driver exists
    const driver = await Driver.findByPk(driverId);
    if (!driver) {
      return res.status(404).json({ error: 'Driver not found' });
    }

    // Get the latest location for the driver
    const latestLocation = await DriverLocation.findOne({
      where: { driver_id: driverId },
      order: [['timestamp', 'DESC']]
    });

    if (!latestLocation) {
      return res.status(404).json({ error: 'No location data found for this driver' });
    }

    return res.status(200).json({
      driverLocation: {
        id: latestLocation.id,
        farmId: latestLocation.farm_id,
        driverId: latestLocation.driver_id,
        latitude: latestLocation.latitude,
        longitude: latestLocation.longitude,
        accuracy: latestLocation.accuracy,
        altitude: latestLocation.altitude,
        speed: latestLocation.speed,
        heading: latestLocation.heading,
        address: latestLocation.address,
        timestamp: latestLocation.timestamp,
        deviceId: latestLocation.device_id,
        batteryLevel: latestLocation.battery_level,
        createdAt: latestLocation.created_at,
        updatedAt: latestLocation.updated_at
      }
    });
  } catch (error) {
    console.error('Error getting latest driver location:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get driver location history by driver ID
export const getDriverLocationHistory = async (req, res) => {
  try {
    const { driverId } = req.params;
    const { startDate, endDate, limit = 100 } = req.query;

    // Check if driver exists
    const driver = await Driver.findByPk(driverId);
    if (!driver) {
      return res.status(404).json({ error: 'Driver not found' });
    }

    // Build the where clause
    const whereClause = {
      driver_id: driverId
    };

    // Date range filtering
    if (startDate || endDate) {
      whereClause.timestamp = {};

      if (startDate) {
        whereClause.timestamp[sequelize.Op.gte] = new Date(startDate);
      }

      if (endDate) {
        whereClause.timestamp[sequelize.Op.lte] = new Date(endDate);
      }
    }

    // Get location history
    const locationHistory = await DriverLocation.findAll({
      where: whereClause,
      limit: parseInt(limit),
      order: [['timestamp', 'DESC']]
    });

    return res.status(200).json({
      driverLocationHistory: locationHistory.map(location => ({
        id: location.id,
        latitude: location.latitude,
        longitude: location.longitude,
        accuracy: location.accuracy,
        altitude: location.altitude,
        speed: location.speed,
        heading: location.heading,
        address: location.address,
        timestamp: location.timestamp,
        deviceId: location.device_id,
        batteryLevel: location.battery_level
      }))
    });
  } catch (error) {
    console.error('Error getting driver location history:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete driver location
export const deleteDriverLocation = async (req, res) => {
  try {
    const { locationId } = req.params;

    const driverLocation = await DriverLocation.findByPk(locationId);
    if (!driverLocation) {
      return res.status(404).json({ error: 'Driver location not found' });
    }

    await driverLocation.destroy();

    return res.status(200).json({
      message: 'Driver location deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting driver location:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete driver location history
export const deleteDriverLocationHistory = async (req, res) => {
  try {
    const { driverId } = req.params;
    const { olderThan } = req.query;

    // Check if driver exists
    const driver = await Driver.findByPk(driverId);
    if (!driver) {
      return res.status(404).json({ error: 'Driver not found' });
    }

    // Build the where clause
    const whereClause = {
      driver_id: driverId
    };

    // If olderThan is provided, only delete locations older than the specified date
    if (olderThan) {
      whereClause.timestamp = {
        [sequelize.Op.lt]: new Date(olderThan)
      };
    }

    // Delete location history
    const deletedCount = await DriverLocation.destroy({
      where: whereClause
    });

    return res.status(200).json({
      message: `${deletedCount} driver location records deleted successfully`
    });
  } catch (error) {
    console.error('Error deleting driver location history:', error);
    return res.status(500).json({ error: error.message });
  }
};
