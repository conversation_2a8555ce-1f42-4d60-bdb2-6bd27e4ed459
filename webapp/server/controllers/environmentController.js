// Controller for environment variables
import dotenv from 'dotenv';

/**
 * Get all environment variables
 * This endpoint is for debugging purposes only and should be restricted to admin users
 */
export const getEnvironmentVariables = async (req, res) => {
  try {
    // Get all environment variables
    const envVars = process.env;
    
    // Filter out sensitive information
    const filteredEnvVars = {};
    for (const key in envVars) {
      // Skip variables that might contain sensitive information
      if (
        key.includes('SECRET') || 
        key.includes('PASSWORD') || 
        key.includes('TOKEN') || 
        key.includes('KEY') ||
        key.includes('CERT')
      ) {
        filteredEnvVars[key] = '[REDACTED]';
      } else {
        filteredEnvVars[key] = envVars[key];
      }
    }

    return res.status(200).json({
      success: true,
      environment: process.env.NODE_ENV || 'development',
      variables: filteredEnvVars
    });
  } catch (error) {
    console.error('Error fetching environment variables:', error);
    
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch environment variables',
      details: error.message
    });
  }
};

/**
 * Get frontend environment variables
 * This endpoint provides environment variables that are available to the frontend
 */
export const getFrontendEnvironmentVariables = async (req, res) => {
  try {
    // Get all environment variables that start with VITE_
    const frontendEnvVars = {};
    for (const key in process.env) {
      if (key.startsWith('VITE_')) {
        frontendEnvVars[key] = process.env[key];
      }
    }

    return res.status(200).json({
      success: true,
      environment: process.env.NODE_ENV || 'development',
      variables: frontendEnvVars
    });
  } catch (error) {
    console.error('Error fetching frontend environment variables:', error);
    
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch frontend environment variables',
      details: error.message
    });
  }
};