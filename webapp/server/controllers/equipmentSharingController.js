import EquipmentSharing from '../models/EquipmentSharing.js';
import Equipment from '../models/Equipment.js';
import Farm from '../models/Farm.js';
import { Op } from 'sequelize';
import { sequelize } from '../config/database.js';

// Get all equipment sharing records for a farm (both as owner and renter)
export const getFarmEquipmentSharing = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all equipment sharing records where the farm is either the owner or renter
    const sharingRecords = await EquipmentSharing.findAll({
      where: {
        [Op.or]: [
          { owner_farm_id: farmId },
          { renter_farm_id: farmId }
        ]
      },
      include: [
        {
          model: Equipment,
          as: 'sharingEquipment',
          attributes: ['id', 'name', 'type', 'manufacturer', 'model', 'year']
        },
        {
          model: Farm,
          as: 'Owner',
          attributes: ['id', 'name']
        },
        {
          model: Farm,
          as: 'Renter',
          attributes: ['id', 'name']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    // Map the records to match the frontend expected structure
    const mappedRecords = sharingRecords.map(record => {
      const plainRecord = record.get({ plain: true });
      return {
        ...plainRecord,
        OwnerFarm: plainRecord.Owner,
        RenterFarm: plainRecord.Renter
      };
    });

    return res.status(200).json(mappedRecords);
  } catch (error) {
    console.error('Error getting farm equipment sharing records:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get equipment sharing records where farm is the owner
export const getOwnedEquipmentSharing = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all equipment sharing records where the farm is the owner
    const sharingRecords = await EquipmentSharing.findAll({
      where: { owner_farm_id: farmId },
      include: [
        {
          model: Equipment,
          as: 'sharingEquipment',
          attributes: ['id', 'name', 'type', 'manufacturer', 'model', 'year']
        },
        {
          model: Farm,
          as: 'Owner',
          attributes: ['id', 'name']
        },
        {
          model: Farm,
          as: 'Renter',
          attributes: ['id', 'name']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    // Map the records to match the frontend expected structure
    const mappedRecords = sharingRecords.map(record => {
      const plainRecord = record.get({ plain: true });
      return {
        ...plainRecord,
        OwnerFarm: plainRecord.Owner,
        RenterFarm: plainRecord.Renter
      };
    });

    return res.status(200).json(mappedRecords);
  } catch (error) {
    console.error('Error getting owned equipment sharing records:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get equipment sharing records where farm is the renter
export const getRentedEquipmentSharing = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all equipment sharing records where the farm is the renter
    const sharingRecords = await EquipmentSharing.findAll({
      where: { renter_farm_id: farmId },
      include: [
        {
          model: Equipment,
          as: 'sharingEquipment',
          attributes: ['id', 'name', 'type', 'manufacturer', 'model', 'year']
        },
        {
          model: Farm,
          as: 'Owner',
          attributes: ['id', 'name']
        },
        {
          model: Farm,
          as: 'Renter',
          attributes: ['id', 'name']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    // Map the records to match the frontend expected structure
    const mappedRecords = sharingRecords.map(record => {
      const plainRecord = record.get({ plain: true });
      return {
        ...plainRecord,
        OwnerFarm: plainRecord.Owner,
        RenterFarm: plainRecord.Renter
      };
    });

    return res.status(200).json(mappedRecords);
  } catch (error) {
    console.error('Error getting rented equipment sharing records:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single equipment sharing record by ID
export const getEquipmentSharingById = async (req, res) => {
  try {
    const { sharingId } = req.params;

    const sharingRecord = await EquipmentSharing.findByPk(sharingId, {
      include: [
        {
          model: Equipment,
          as: 'sharingEquipment',
          attributes: ['id', 'name', 'type', 'manufacturer', 'model', 'year']
        },
        {
          model: Farm,
          as: 'Owner',
          attributes: ['id', 'name']
        },
        {
          model: Farm,
          as: 'Renter',
          attributes: ['id', 'name']
        }
      ]
    });

    if (!sharingRecord) {
      return res.status(404).json({ error: 'Equipment sharing record not found' });
    }

    // Map the record to match the frontend expected structure
    const plainRecord = sharingRecord.get({ plain: true });
    const mappedRecord = {
      ...plainRecord,
      OwnerFarm: plainRecord.Owner,
      RenterFarm: plainRecord.Renter
    };

    return res.status(200).json(mappedRecord);
  } catch (error) {
    console.error('Error getting equipment sharing record:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new equipment sharing record
export const createEquipmentSharing = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      equipmentId, 
      ownerFarmId, 
      renterFarmId, 
      startDate, 
      endDate, 
      rentalCost, 
      rentalCostType, 
      notes 
    } = req.body;

    // Validate required fields
    if (!equipmentId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Equipment ID is required' });
    }

    if (!ownerFarmId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Owner Farm ID is required' });
    }

    if (!renterFarmId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Renter Farm ID is required' });
    }

    if (!startDate) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Start date is required' });
    }

    if (!endDate) {
      await transaction.rollback();
      return res.status(400).json({ error: 'End date is required' });
    }

    // Ensure owner and renter farms are different
    if (ownerFarmId === renterFarmId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Owner and renter farms must be different' });
    }

    // Find equipment to ensure it exists and belongs to the owner farm
    const equipment = await Equipment.findOne({
      where: {
        id: equipmentId,
        farm_id: ownerFarmId
      }
    });

    if (!equipment) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Equipment not found or does not belong to the owner farm' });
    }

    // Find owner farm to ensure it exists
    const ownerFarm = await Farm.findByPk(ownerFarmId);
    if (!ownerFarm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Owner farm not found' });
    }

    // Find renter farm to ensure it exists
    const renterFarm = await Farm.findByPk(renterFarmId);
    if (!renterFarm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Renter farm not found' });
    }

    // Check for overlapping sharing periods for the same equipment
    const overlappingSharing = await EquipmentSharing.findOne({
      where: {
        equipment_id: equipmentId,
        status: {
          [Op.in]: ['pending', 'approved', 'active']
        },
        [Op.or]: [
          {
            // New sharing starts during an existing sharing
            start_date: {
              [Op.lte]: endDate
            },
            end_date: {
              [Op.gte]: startDate
            }
          }
        ]
      }
    });

    if (overlappingSharing) {
      await transaction.rollback();
      return res.status(400).json({ 
        error: 'Equipment is already shared during this period',
        conflictingSharing: overlappingSharing
      });
    }

    // Create equipment sharing record
    const sharingRecord = await EquipmentSharing.create({
      equipment_id: equipmentId,
      owner_farm_id: ownerFarmId,
      renter_farm_id: renterFarmId,
      start_date: startDate,
      end_date: endDate,
      rental_cost: rentalCost,
      rental_cost_type: rentalCostType,
      status: 'pending',
      notes
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({ 
      message: 'Equipment sharing request created successfully',
      sharingRecord 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating equipment sharing record:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update an equipment sharing record
export const updateEquipmentSharing = async (req, res) => {
  try {
    const { sharingId } = req.params;
    const { 
      startDate, 
      endDate, 
      rentalCost, 
      rentalCostType, 
      notes 
    } = req.body;

    // Find sharing record to ensure it exists
    const sharingRecord = await EquipmentSharing.findByPk(sharingId);
    if (!sharingRecord) {
      return res.status(404).json({ error: 'Equipment sharing record not found' });
    }

    // Only allow updates if status is pending
    if (sharingRecord.status !== 'pending') {
      return res.status(400).json({ 
        error: 'Only pending sharing requests can be updated' 
      });
    }

    // Update sharing record
    await sharingRecord.update({
      start_date: startDate !== undefined ? startDate : sharingRecord.start_date,
      end_date: endDate !== undefined ? endDate : sharingRecord.end_date,
      rental_cost: rentalCost !== undefined ? rentalCost : sharingRecord.rental_cost,
      rental_cost_type: rentalCostType !== undefined ? rentalCostType : sharingRecord.rental_cost_type,
      notes: notes !== undefined ? notes : sharingRecord.notes
    });

    return res.status(200).json({ 
      message: 'Equipment sharing record updated successfully',
      sharingRecord 
    });
  } catch (error) {
    console.error('Error updating equipment sharing record:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update the status of an equipment sharing record
export const updateSharingStatus = async (req, res) => {
  try {
    const { sharingId } = req.params;
    const { status } = req.body;

    // Validate status
    const validStatuses = ['pending', 'approved', 'active', 'completed', 'cancelled', 'rejected'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ 
        error: 'Invalid status. Must be one of: ' + validStatuses.join(', ') 
      });
    }

    // Find sharing record to ensure it exists
    const sharingRecord = await EquipmentSharing.findByPk(sharingId);
    if (!sharingRecord) {
      return res.status(404).json({ error: 'Equipment sharing record not found' });
    }

    // Update status
    await sharingRecord.update({ status });

    return res.status(200).json({ 
      message: 'Equipment sharing status updated successfully',
      sharingRecord 
    });
  } catch (error) {
    console.error('Error updating equipment sharing status:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete an equipment sharing record
export const deleteEquipmentSharing = async (req, res) => {
  try {
    const { sharingId } = req.params;

    // Find sharing record to ensure it exists
    const sharingRecord = await EquipmentSharing.findByPk(sharingId);
    if (!sharingRecord) {
      return res.status(404).json({ error: 'Equipment sharing record not found' });
    }

    // Only allow deletion if status is pending or cancelled
    if (!['pending', 'cancelled', 'rejected'].includes(sharingRecord.status)) {
      return res.status(400).json({ 
        error: 'Only pending, cancelled, or rejected sharing requests can be deleted' 
      });
    }

    // Delete sharing record
    await sharingRecord.destroy();

    return res.status(200).json({ 
      message: 'Equipment sharing record deleted successfully' 
    });
  } catch (error) {
    console.error('Error deleting equipment sharing record:', error);
    return res.status(500).json({ error: error.message });
  }
};
