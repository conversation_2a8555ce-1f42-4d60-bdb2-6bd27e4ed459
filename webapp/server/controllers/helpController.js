import HelpGuide from '../models/HelpGuide.js';
import HelpTip from '../models/HelpTip.js';
import UserHelpTipDismissal from '../models/UserHelpTipDismissal.js';
import GettingStartedTask from '../models/GettingStartedTask.js';
import UserGettingStartedProgress from '../models/UserGettingStartedProgress.js';
import { sequelize, Op } from '../config/database.js';

// ==================== Help Guides ====================

// Create a new help guide
export const createHelpGuide = async (req, res) => {
  try {
    const {
      title,
      slug,
      content,
      category,
      subcategory,
      tags = [],
      isPublished = true,
      order = 0
    } = req.body;

    // Validate required fields
    if (!title || !slug || !content || !category) {
      return res.status(400).json({ error: 'Title, slug, content, and category are required' });
    }

    // Check if slug already exists
    const existingGuide = await HelpGuide.findOne({ where: { slug } });
    if (existingGuide) {
      return res.status(400).json({ error: 'A guide with this slug already exists' });
    }

    // Create the help guide
    const guide = await HelpGuide.create({
      title,
      slug,
      content,
      category,
      subcategory,
      tags,
      is_published: isPublished,
      order
    });

    return res.status(201).json({
      guide: {
        id: guide.id,
        title: guide.title,
        slug: guide.slug,
        content: guide.content,
        category: guide.category,
        subcategory: guide.subcategory,
        tags: guide.tags,
        isPublished: guide.is_published,
        order: guide.order,
        createdAt: guide.created_at,
        updatedAt: guide.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating help guide:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all help guides with optional filtering
export const getHelpGuides = async (req, res) => {
  try {
    const {
      category,
      subcategory,
      tag,
      isPublished,
      search,
      limit = 100,
      offset = 0
    } = req.query;

    // Build the where clause based on query parameters
    const whereClause = {};

    if (category) {
      whereClause.category = category;
    }

    if (subcategory) {
      whereClause.subcategory = subcategory;
    }

    if (tag) {
      // Use literal SQL with proper type casting to avoid text[] vs varchar[] mismatch
      whereClause[sequelize.literal(`tags @> ARRAY['${tag}']::text[]`)] = true;
    }

    if (isPublished !== undefined) {
      whereClause.is_published = isPublished === 'true';
    }

    if (search) {
      whereClause[Op.or] = [
        { title: { [Op.iLike]: `%${search}%` } },
        { content: { [Op.iLike]: `%${search}%` } },
        { slug: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // Get guides with pagination
    const guides = await HelpGuide.findAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [
        ['category', 'ASC'],
        ['subcategory', 'ASC'],
        ['order', 'ASC'],
        ['title', 'ASC']
      ]
    });

    // Get total count for pagination
    const totalCount = await HelpGuide.count({ where: whereClause });

    return res.status(200).json({
      guides: guides.map(guide => ({
        id: guide.id,
        title: guide.title,
        slug: guide.slug,
        content: guide.content,
        category: guide.category,
        subcategory: guide.subcategory,
        tags: guide.tags,
        isPublished: guide.is_published,
        order: guide.order,
        createdAt: guide.created_at,
        updatedAt: guide.updated_at
      })),
      totalCount
    });
  } catch (error) {
    console.error('Error getting help guides:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get help guide by ID or slug
export const getHelpGuide = async (req, res) => {
  try {
    const { idOrSlug } = req.params;

    let guide;

    // Check if the parameter is a UUID
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (uuidRegex.test(idOrSlug)) {
      guide = await HelpGuide.findByPk(idOrSlug);
    } else {
      guide = await HelpGuide.findOne({ where: { slug: idOrSlug } });
    }

    if (!guide) {
      return res.status(404).json({ error: 'Help guide not found' });
    }

    return res.status(200).json({
      guide: {
        id: guide.id,
        title: guide.title,
        slug: guide.slug,
        content: guide.content,
        category: guide.category,
        subcategory: guide.subcategory,
        tags: guide.tags,
        isPublished: guide.is_published,
        order: guide.order,
        createdAt: guide.created_at,
        updatedAt: guide.updated_at
      }
    });
  } catch (error) {
    console.error('Error getting help guide:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update help guide
export const updateHelpGuide = async (req, res) => {
  try {
    const { guideId } = req.params;
    const {
      title,
      slug,
      content,
      category,
      subcategory,
      tags,
      isPublished,
      order
    } = req.body;

    const guide = await HelpGuide.findByPk(guideId);
    if (!guide) {
      return res.status(404).json({ error: 'Help guide not found' });
    }

    // If slug is being changed, check if the new slug already exists
    if (slug && slug !== guide.slug) {
      const existingGuide = await HelpGuide.findOne({ where: { slug } });
      if (existingGuide) {
        return res.status(400).json({ error: 'A guide with this slug already exists' });
      }
    }

    // Update guide fields if provided
    if (title !== undefined) guide.title = title;
    if (slug !== undefined) guide.slug = slug;
    if (content !== undefined) guide.content = content;
    if (category !== undefined) guide.category = category;
    if (subcategory !== undefined) guide.subcategory = subcategory;
    if (tags !== undefined) guide.tags = tags;
    if (isPublished !== undefined) guide.is_published = isPublished;
    if (order !== undefined) guide.order = order;

    await guide.save();

    return res.status(200).json({
      guide: {
        id: guide.id,
        title: guide.title,
        slug: guide.slug,
        content: guide.content,
        category: guide.category,
        subcategory: guide.subcategory,
        tags: guide.tags,
        isPublished: guide.is_published,
        order: guide.order,
        createdAt: guide.created_at,
        updatedAt: guide.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating help guide:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete help guide
export const deleteHelpGuide = async (req, res) => {
  try {
    const { guideId } = req.params;

    const guide = await HelpGuide.findByPk(guideId);
    if (!guide) {
      return res.status(404).json({ error: 'Help guide not found' });
    }

    await guide.destroy();

    return res.status(200).json({
      message: 'Help guide deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting help guide:', error);
    return res.status(500).json({ error: error.message });
  }
};

// ==================== Help Tips ====================

// Create a new help tip
export const createHelpTip = async (req, res) => {
  try {
    const {
      title,
      content,
      pagePath,
      elementSelector,
      position = 'right',
      order = 0,
      isActive = true
    } = req.body;

    // Validate required fields
    if (!title || !content || !pagePath) {
      return res.status(400).json({ error: 'Title, content, and pagePath are required' });
    }

    // Create the help tip
    const tip = await HelpTip.create({
      title,
      content,
      page_path: pagePath,
      element_selector: elementSelector,
      position,
      order,
      is_active: isActive
    });

    return res.status(201).json({
      tip: {
        id: tip.id,
        title: tip.title,
        content: tip.content,
        pagePath: tip.page_path,
        elementSelector: tip.element_selector,
        position: tip.position,
        order: tip.order,
        isActive: tip.is_active,
        createdAt: tip.created_at,
        updatedAt: tip.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating help tip:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all help tips with optional filtering
export const getHelpTips = async (req, res) => {
  try {
    const {
      pagePath,
      isActive,
      search,
      limit = 100,
      offset = 0
    } = req.query;

    // Build the where clause based on query parameters
    const whereClause = {};

    if (pagePath) {
      whereClause.page_path = pagePath;
    }

    if (isActive !== undefined) {
      whereClause.is_active = isActive === 'true';
    }

    if (search) {
      whereClause[Op.or] = [
        { title: { [Op.iLike]: `%${search}%` } },
        { content: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // Get tips with pagination
    const tips = await HelpTip.findAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [
        ['page_path', 'ASC'],
        ['order', 'ASC']
      ]
    });

    // Get total count for pagination
    const totalCount = await HelpTip.count({ where: whereClause });

    return res.status(200).json({
      tips: tips.map(tip => ({
        id: tip.id,
        title: tip.title,
        content: tip.content,
        pagePath: tip.page_path,
        elementSelector: tip.element_selector,
        position: tip.position,
        order: tip.order,
        isActive: tip.is_active,
        createdAt: tip.created_at,
        updatedAt: tip.updated_at
      })),
      totalCount
    });
  } catch (error) {
    console.error('Error getting help tips:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get help tip by ID
export const getHelpTip = async (req, res) => {
  try {
    const { tipId } = req.params;

    const tip = await HelpTip.findByPk(tipId);
    if (!tip) {
      return res.status(404).json({ error: 'Help tip not found' });
    }

    return res.status(200).json({
      tip: {
        id: tip.id,
        title: tip.title,
        content: tip.content,
        pagePath: tip.page_path,
        elementSelector: tip.element_selector,
        position: tip.position,
        order: tip.order,
        isActive: tip.is_active,
        createdAt: tip.created_at,
        updatedAt: tip.updated_at
      }
    });
  } catch (error) {
    console.error('Error getting help tip:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update help tip
export const updateHelpTip = async (req, res) => {
  try {
    const { tipId } = req.params;
    const {
      title,
      content,
      pagePath,
      elementSelector,
      position,
      order,
      isActive
    } = req.body;

    const tip = await HelpTip.findByPk(tipId);
    if (!tip) {
      return res.status(404).json({ error: 'Help tip not found' });
    }

    // Update tip fields if provided
    if (title !== undefined) tip.title = title;
    if (content !== undefined) tip.content = content;
    if (pagePath !== undefined) tip.page_path = pagePath;
    if (elementSelector !== undefined) tip.element_selector = elementSelector;
    if (position !== undefined) tip.position = position;
    if (order !== undefined) tip.order = order;
    if (isActive !== undefined) tip.is_active = isActive;

    await tip.save();

    return res.status(200).json({
      tip: {
        id: tip.id,
        title: tip.title,
        content: tip.content,
        pagePath: tip.page_path,
        elementSelector: tip.element_selector,
        position: tip.position,
        order: tip.order,
        isActive: tip.is_active,
        createdAt: tip.created_at,
        updatedAt: tip.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating help tip:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete help tip
export const deleteHelpTip = async (req, res) => {
  try {
    const { tipId } = req.params;

    const tip = await HelpTip.findByPk(tipId);
    if (!tip) {
      return res.status(404).json({ error: 'Help tip not found' });
    }

    await tip.destroy();

    return res.status(200).json({
      message: 'Help tip deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting help tip:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Dismiss help tip for user
export const dismissHelpTip = async (req, res) => {
  try {
    const { tipId } = req.params;
    const userId = req.user.id; // From auth middleware

    // Check if tip exists
    const tip = await HelpTip.findByPk(tipId);
    if (!tip) {
      return res.status(404).json({ error: 'Help tip not found' });
    }

    // Check if already dismissed
    const existingDismissal = await UserHelpTipDismissal.findOne({
      where: {
        user_id: userId,
        help_tip_id: tipId
      }
    });

    if (existingDismissal) {
      return res.status(200).json({
        message: 'Help tip already dismissed',
        dismissal: {
          id: existingDismissal.id,
          userId: existingDismissal.user_id,
          tipId: existingDismissal.help_tip_id,
          dismissedAt: existingDismissal.dismissed_at
        }
      });
    }

    // Create dismissal record
    const dismissal = await UserHelpTipDismissal.create({
      user_id: userId,
      help_tip_id: tipId
    });

    return res.status(201).json({
      message: 'Help tip dismissed successfully',
      dismissal: {
        id: dismissal.id,
        userId: dismissal.user_id,
        tipId: dismissal.help_tip_id,
        dismissedAt: dismissal.dismissed_at
      }
    });
  } catch (error) {
    console.error('Error dismissing help tip:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get dismissed help tips for user
export const getDismissedHelpTips = async (req, res) => {
  try {
    const userId = req.user.id; // From auth middleware

    const dismissals = await UserHelpTipDismissal.findAll({
      where: { user_id: userId },
      include: [{
        model: HelpTip,
        required: false // Use LEFT JOIN to handle cases where HelpTip might be deleted
      }]
    });

    return res.status(200).json({
      dismissals: dismissals.map(dismissal => ({
        id: dismissal.id,
        tipId: dismissal.help_tip_id,
        dismissedAt: dismissal.dismissed_at,
        tip: dismissal.HelpTip ? {
          id: dismissal.HelpTip.id,
          title: dismissal.HelpTip.title,
          pagePath: dismissal.HelpTip.page_path
        } : null
      }))
    });
  } catch (error) {
    console.error('Error getting dismissed help tips:', error);
    console.error('Error details:', {
      message: error.message,
      name: error.name,
      sql: error.sql
    });

    // If it's an association error, try a fallback approach
    if (error.message.includes('is not associated')) {
      try {
        const userId = req.user.id; // Re-declare userId in this scope
        console.log('Attempting fallback query without include...');
        const dismissals = await UserHelpTipDismissal.findAll({
          where: { user_id: userId }
        });

        // Manually fetch help tips for each dismissal
        const dismissalsWithTips = await Promise.all(
          dismissals.map(async (dismissal) => {
            let tip = null;
            try {
              tip = await HelpTip.findByPk(dismissal.help_tip_id);
            } catch (tipError) {
              console.error('Error fetching help tip:', tipError);
            }

            return {
              id: dismissal.id,
              tipId: dismissal.help_tip_id,
              dismissedAt: dismissal.dismissed_at,
              tip: tip ? {
                id: tip.id,
                title: tip.title,
                pagePath: tip.page_path
              } : null
            };
          })
        );

        return res.status(200).json({
          dismissals: dismissalsWithTips
        });
      } catch (fallbackError) {
        console.error('Fallback query also failed:', fallbackError);
        return res.status(500).json({ error: 'Unable to retrieve dismissed help tips' });
      }
    }

    return res.status(500).json({ error: error.message });
  }
};

// Reset all dismissed help tips for user
export const resetDismissedHelpTips = async (req, res) => {
  try {
    const userId = req.user.id; // From auth middleware

    await UserHelpTipDismissal.destroy({
      where: { user_id: userId }
    });

    return res.status(200).json({
      message: 'All dismissed help tips have been reset'
    });
  } catch (error) {
    console.error('Error resetting dismissed help tips:', error);
    return res.status(500).json({ error: error.message });
  }
};

// ==================== Getting Started Tasks ====================

// Create a new getting started task
export const createGettingStartedTask = async (req, res) => {
  try {
    const {
      title,
      description,
      linkPath,
      icon,
      order = 0,
      isActive = true,
      userType = 'all'
    } = req.body;

    // Validate required fields
    if (!title || !description) {
      return res.status(400).json({ error: 'Title and description are required' });
    }

    // Create the task
    const task = await GettingStartedTask.create({
      title,
      description,
      link_path: linkPath,
      icon,
      order,
      is_active: isActive,
      user_type: userType
    });

    return res.status(201).json({
      task: {
        id: task.id,
        title: task.title,
        description: task.description,
        linkPath: task.link_path,
        icon: task.icon,
        order: task.order,
        isActive: task.is_active,
        userType: task.user_type,
        createdAt: task.created_at,
        updatedAt: task.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating getting started task:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all getting started tasks with optional filtering
export const getGettingStartedTasks = async (req, res) => {
  try {
    const {
      userType,
      isActive,
      search,
      limit = 100,
      offset = 0
    } = req.query;

    // Build the where clause based on query parameters
    const whereClause = {};

    if (userType) {
      whereClause[Op.or] = [
        { user_type: userType },
        { user_type: 'all' }
      ];
    }

    if (isActive !== undefined) {
      whereClause.is_active = isActive === 'true';
    }

    if (search) {
      whereClause[Op.or] = [
        { title: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // Get tasks with pagination
    const tasks = await GettingStartedTask.findAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['order', 'ASC']]
    });

    // Get total count for pagination
    const totalCount = await GettingStartedTask.count({ where: whereClause });

    return res.status(200).json({
      tasks: tasks.map(task => ({
        id: task.id,
        title: task.title,
        description: task.description,
        linkPath: task.link_path,
        icon: task.icon,
        order: task.order,
        isActive: task.is_active,
        userType: task.user_type,
        createdAt: task.created_at,
        updatedAt: task.updated_at
      })),
      totalCount
    });
  } catch (error) {
    console.error('Error getting getting started tasks:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get getting started task by ID
export const getGettingStartedTask = async (req, res) => {
  try {
    const { taskId } = req.params;

    const task = await GettingStartedTask.findByPk(taskId);
    if (!task) {
      return res.status(404).json({ error: 'Getting started task not found' });
    }

    return res.status(200).json({
      task: {
        id: task.id,
        title: task.title,
        description: task.description,
        linkPath: task.link_path,
        icon: task.icon,
        order: task.order,
        isActive: task.is_active,
        userType: task.user_type,
        createdAt: task.created_at,
        updatedAt: task.updated_at
      }
    });
  } catch (error) {
    console.error('Error getting getting started task:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update getting started task
export const updateGettingStartedTask = async (req, res) => {
  try {
    const { taskId } = req.params;
    const {
      title,
      description,
      linkPath,
      icon,
      order,
      isActive,
      userType
    } = req.body;

    const task = await GettingStartedTask.findByPk(taskId);
    if (!task) {
      return res.status(404).json({ error: 'Getting started task not found' });
    }

    // Update task fields if provided
    if (title !== undefined) task.title = title;
    if (description !== undefined) task.description = description;
    if (linkPath !== undefined) task.link_path = linkPath;
    if (icon !== undefined) task.icon = icon;
    if (order !== undefined) task.order = order;
    if (isActive !== undefined) task.is_active = isActive;
    if (userType !== undefined) task.user_type = userType;

    await task.save();

    return res.status(200).json({
      task: {
        id: task.id,
        title: task.title,
        description: task.description,
        linkPath: task.link_path,
        icon: task.icon,
        order: task.order,
        isActive: task.is_active,
        userType: task.user_type,
        createdAt: task.created_at,
        updatedAt: task.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating getting started task:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete getting started task
export const deleteGettingStartedTask = async (req, res) => {
  try {
    const { taskId } = req.params;

    const task = await GettingStartedTask.findByPk(taskId);
    if (!task) {
      return res.status(404).json({ error: 'Getting started task not found' });
    }

    await task.destroy();

    return res.status(200).json({
      message: 'Getting started task deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting getting started task:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Mark getting started task as completed for user
export const completeGettingStartedTask = async (req, res) => {
  try {
    const { taskId } = req.params;
    const userId = req.user.id; // From auth middleware

    // Check if task exists
    const task = await GettingStartedTask.findByPk(taskId);
    if (!task) {
      return res.status(404).json({ error: 'Getting started task not found' });
    }

    // Check if already completed
    let progress = await UserGettingStartedProgress.findOne({
      where: {
        user_id: userId,
        task_id: taskId
      }
    });

    if (progress) {
      // Update if exists
      progress.completed = true;
      progress.completed_at = new Date();
      await progress.save();
    } else {
      // Create new progress record
      progress = await UserGettingStartedProgress.create({
        user_id: userId,
        task_id: taskId,
        completed: true,
        completed_at: new Date()
      });
    }

    return res.status(200).json({
      message: 'Task marked as completed',
      progress: {
        id: progress.id,
        userId: progress.user_id,
        taskId: progress.task_id,
        completed: progress.completed,
        completedAt: progress.completed_at
      }
    });
  } catch (error) {
    console.error('Error completing getting started task:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get user's progress on getting started tasks
export const getUserGettingStartedProgress = async (req, res) => {
  try {
    const userId = req.user.id; // From auth middleware
    const { userType = req.user.user_type } = req.query;

    // Get all active tasks for the user's type
    const tasks = await GettingStartedTask.findAll({
      where: {
        is_active: true,
        [Op.or]: [
          { user_type: userType },
          { user_type: 'all' }
        ]
      },
      order: [['order', 'ASC']]
    });

    // Get user's progress
    const progress = await UserGettingStartedProgress.findAll({
      where: { user_id: userId },
      include: [{
        model: GettingStartedTask,
        required: false // Use LEFT JOIN to handle cases where task might be deleted
      }]
    });

    // Map tasks with progress
    const tasksWithProgress = tasks.map(task => {
      const taskProgress = progress.find(p => p.task_id === task.id);
      return {
        id: task.id,
        title: task.title,
        description: task.description,
        linkPath: task.link_path,
        icon: task.icon,
        order: task.order,
        completed: taskProgress ? taskProgress.completed : false,
        completedAt: taskProgress ? taskProgress.completed_at : null
      };
    });

    // Calculate overall progress
    const completedCount = tasksWithProgress.filter(task => task.completed).length;
    const totalCount = tasksWithProgress.length;
    const progressPercentage = totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0;

    return res.status(200).json({
      tasks: tasksWithProgress,
      progress: {
        completed: completedCount,
        total: totalCount,
        percentage: progressPercentage
      }
    });
  } catch (error) {
    console.error('Error getting user getting started progress:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Reset user's getting started progress
export const resetUserGettingStartedProgress = async (req, res) => {
  try {
    const userId = req.user.id; // From auth middleware

    await UserGettingStartedProgress.destroy({
      where: { user_id: userId }
    });

    return res.status(200).json({
      message: 'All getting started progress has been reset'
    });
  } catch (error) {
    console.error('Error resetting getting started progress:', error);
    return res.status(500).json({ error: error.message });
  }
};
