import User from '../models/User.js';
import { sequelize } from '../config/database.js';
import bcrypt from 'bcryptjs';
import UserFarm from '../models/UserFarm.js';
import Role from '../models/Role.js';
import Farm from '../models/Farm.js';
import UserHelpTipDismissal from '../models/UserHelpTipDismissal.js';
import matrixClientService from '../services/matrixClientService.js';

// Helper function to find or create a role by name and farm ID
const findOrCreateRoleByName = async (roleName, farmId, transaction) => {
  try {
    // First, try to find an existing role for this farm with this name
    let role = await Role.findOne({
      where: {
        name: roleName,
        farm_id: farmId
      },
      transaction
    });

    // If role exists, return it
    if (role) {
      return role;
    }

    // If no farm-specific role exists, try to find a global role with this name
    role = await Role.findOne({
      where: {
        name: roleName,
        farm_id: null
      },
      transaction
    });

    // If global role exists, return it
    if (role) {
      return role;
    }

    // If no role exists at all, create a new one for this farm
    console.log(`Creating new role '${roleName}' for farm ${farmId}`);
    role = await Role.create({
      name: roleName,
      farm_id: farmId,
      description: `${roleName} role for farm ${farmId}`,
      is_system_role: true
    }, { transaction });

    return role;
  } catch (error) {
    console.error(`Error finding or creating role '${roleName}' for farm ${farmId}:`, error);
    throw error;
  }
};

// Create a new user (admin only)
export const createUser = async (req, res) => {
  try {
    const { 
      email, 
      password, 
      firstName, 
      lastName, 
      phoneNumber, 
      userType = 'farmer', 
      isBusinessOwner = false,
      isApproved = true,
      isGlobalAdmin = false,
      farmId = null
    } = req.body;

    // Validate user type
    const validUserTypes = ['farmer', 'supplier', 'vet', 'admin', 'accountant'];
    if (!validUserTypes.includes(userType)) {
      return res.status(400).json({ error: 'Invalid user type' });
    }

    // Check if user already exists
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      return res.status(400).json({ error: 'Email already in use' });
    }

    // Create new user
    const user = await User.create({
      email,
      password_hash: password, // Will be hashed by the model hook
      first_name: firstName,
      last_name: lastName,
      phone_number: phoneNumber,
      user_type: userType,
      is_business_owner: isBusinessOwner,
      is_approved: isApproved,
      is_global_admin: isGlobalAdmin,
      farm_id: farmId
    });

    // If farmId is provided, associate user with farm
    if (farmId) {
      // Find or create the farm_employee role
      const employeeRole = await findOrCreateRoleByName('farm_employee', farmId);

      const userFarm = await UserFarm.create({
        user_id: user.id,
        farm_id: farmId,
        role: 'farm_employee', // Default role
        role_id: employeeRole.id
      });

      // Update menu preferences based on the assigned role
      try {
        console.log('Updating menu preferences based on farm_employee role');
        // Import the menu utility function
        const { updateMenuPreferencesByRole } = await import('../utils/menuUtils.js');

        // Update menu preferences with the utility function
        await updateMenuPreferencesByRole(user.id, employeeRole);
        console.log('Menu preferences updated with role-specific items');
      } catch (menuUpdateError) {
        console.error('Error updating menu preferences with role-specific items:', menuUpdateError);
        // Don't fail the operation if this fails
      }
    }

    // Ensure the user exists in Matrix
    try {
      console.log(`Ensuring user ${user.id} exists in Matrix...`);
      await matrixClientService.ensureMatrixUser(user.id);
      console.log(`User ${user.id} successfully created/verified in Matrix`);
    } catch (matrixError) {
      console.error('Error ensuring user exists in Matrix:', matrixError);
      // Don't fail the operation if Matrix creation fails
    }

    return res.status(201).json({
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        phoneNumber: user.phone_number,
        userType: user.user_type,
        isBusinessOwner: user.is_business_owner,
        isApproved: user.is_approved,
        twoFactorEnabled: user.two_factor_enabled,
        is_global_admin: user.is_global_admin,
        farm_id: user.farm_id,
        created_at: user.created_at,
        updated_at: user.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating user:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all users with optional filtering
export const getUsers = async (req, res) => {
  try {
    const { 
      userType, 
      isBusinessOwner, 
      isApproved, 
      search,
      limit = 100,
      offset = 0
    } = req.query;

    // Build the where clause based on query parameters
    const whereClause = {};

    if (userType) {
      whereClause.user_type = userType;
    }

    if (isBusinessOwner !== undefined) {
      whereClause.is_business_owner = isBusinessOwner === 'true';
    }

    if (isApproved !== undefined) {
      whereClause.is_approved = isApproved === 'true';
    }

    if (search) {
      whereClause[sequelize.Op.or] = [
        { email: { [sequelize.Op.iLike]: `%${search}%` } },
        { first_name: { [sequelize.Op.iLike]: `%${search}%` } },
        { last_name: { [sequelize.Op.iLike]: `%${search}%` } }
      ];
    }

    // Get users with pagination
    const users = await User.findAll({
      where: whereClause,
      attributes: { 
        exclude: ['password_hash', 'reset_password_token', 'reset_password_expires', 'two_factor_secret'] 
      },
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']]
    });

    // Get total count for pagination
    const totalCount = await User.count({ where: whereClause });

    return res.status(200).json({
      users: users.map(user => ({
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        phoneNumber: user.phone_number,
        userType: user.user_type,
        isBusinessOwner: user.is_business_owner,
        isApproved: user.is_approved,
        twoFactorEnabled: user.two_factor_enabled,
        is_global_admin: user.is_global_admin,
        farm_id: user.farm_id,
        created_at: user.created_at,
        updated_at: user.updated_at
      })),
      totalCount
    });
  } catch (error) {
    console.error('Error getting users:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get user by ID
export const getUserById = async (req, res) => {
  try {
    const { userId } = req.params;

    const user = await User.findByPk(userId, {
      attributes: { 
        exclude: ['password_hash', 'reset_password_token', 'reset_password_expires', 'two_factor_secret'] 
      }
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    return res.status(200).json({
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        phoneNumber: user.phone_number,
        userType: user.user_type,
        isBusinessOwner: user.is_business_owner,
        isApproved: user.is_approved,
        twoFactorEnabled: user.two_factor_enabled,
        is_global_admin: user.is_global_admin,
        farm_id: user.farm_id,
        created_at: user.created_at,
        updated_at: user.updated_at
      }
    });
  } catch (error) {
    console.error('Error getting user by ID:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update user
export const updateUser = async (req, res) => {
  try {
    const { userId } = req.params;
    const { 
      email, 
      firstName, 
      lastName, 
      phoneNumber, 
      userType, 
      isBusinessOwner, 
      isApproved,
      isGlobalAdmin,
      farmId
    } = req.body;

    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Update user fields if provided
    if (email !== undefined) user.email = email;
    if (firstName !== undefined) user.first_name = firstName;
    if (lastName !== undefined) user.last_name = lastName;
    if (phoneNumber !== undefined) user.phone_number = phoneNumber;
    if (userType !== undefined) user.user_type = userType;
    if (isBusinessOwner !== undefined) user.is_business_owner = isBusinessOwner;
    if (isApproved !== undefined) user.is_approved = isApproved;
    if (isGlobalAdmin !== undefined) user.is_global_admin = isGlobalAdmin;
    if (farmId !== undefined) user.farm_id = farmId;

    await user.save();

    // Ensure the user exists in Matrix
    try {
      console.log(`Ensuring user ${user.id} exists in Matrix after update...`);
      await matrixClientService.ensureMatrixUser(user.id);
      console.log(`User ${user.id} successfully created/verified in Matrix after update`);
    } catch (matrixError) {
      console.error('Error ensuring user exists in Matrix after update:', matrixError);
      // Don't fail the operation if Matrix creation fails
    }

    return res.status(200).json({
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        phoneNumber: user.phone_number,
        userType: user.user_type,
        isBusinessOwner: user.is_business_owner,
        isApproved: user.is_approved,
        twoFactorEnabled: user.two_factor_enabled,
        is_global_admin: user.is_global_admin,
        farm_id: user.farm_id,
        created_at: user.created_at,
        updated_at: user.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating user:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete user
export const deleteUser = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { userId } = req.params;

    const user = await User.findByPk(userId, { transaction });
    if (!user) {
      await transaction.rollback();
      return res.status(404).json({ error: 'User not found' });
    }

    // Find all farms owned by the user
    const ownedFarms = await UserFarm.findAll({
      where: { 
        user_id: userId,
        role: 'farm_owner'
      },
      transaction
    });

    // Process each farm owned by the user
    for (const userFarm of ownedFarms) {
      const farmId = userFarm.farm_id;

      // Find all users associated with this farm
      const farmUsers = await UserFarm.findAll({
        where: { farm_id: farmId },
        transaction
      });

      // Remove farm associations for all users except the owner
      for (const farmUser of farmUsers) {
        if (farmUser.user_id !== userId) {
          // Delete the association
          await farmUser.destroy({ transaction });

          // Check if the user has any other farms
          const otherFarms = await UserFarm.count({
            where: { user_id: farmUser.user_id },
            transaction
          });

          // If user has no other farms, delete the user
          if (otherFarms === 0) {
            const userToDelete = await User.findByPk(farmUser.user_id, { transaction });
            if (userToDelete) {
              await userToDelete.destroy({ transaction });
            }
          }
        }
      }

      // Delete the farm
      const farm = await Farm.findByPk(farmId, { transaction });
      if (farm) {
        await farm.destroy({ transaction });
      }
    }

    // Delete user help tip dismissals first to avoid foreign key constraint violation
    await UserHelpTipDismissal.destroy({
      where: { user_id: userId },
      transaction
    });

    // Finally, delete the user
    await user.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({
      message: 'User and associated farms deleted successfully'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting user:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Approve business user
export const approveBusinessUser = async (req, res) => {
  try {
    const { userId } = req.params;

    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    if (!user.is_business_owner) {
      return res.status(400).json({ error: 'User is not a business owner' });
    }

    user.is_approved = true;
    await user.save();

    // Ensure the user exists in Matrix
    try {
      console.log(`Ensuring user ${user.id} exists in Matrix after business approval...`);
      await matrixClientService.ensureMatrixUser(user.id);
      console.log(`User ${user.id} successfully created/verified in Matrix after business approval`);
    } catch (matrixError) {
      console.error('Error ensuring user exists in Matrix after business approval:', matrixError);
      // Don't fail the operation if Matrix creation fails
    }

    return res.status(200).json({
      message: 'Business user approved successfully',
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        userType: user.user_type,
        isBusinessOwner: user.is_business_owner,
        isApproved: user.is_approved
      }
    });
  } catch (error) {
    console.error('Error approving business user:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update current user (me)
export const updateCurrentUser = async (req, res) => {
  try {
    const userId = req.user.id; // From auth middleware
    const { 
      firstName, 
      lastName, 
      phoneNumber,
      help_tips_disabled
    } = req.body;

    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Update user fields if provided
    if (firstName !== undefined) user.first_name = firstName;
    if (lastName !== undefined) user.last_name = lastName;
    if (phoneNumber !== undefined) user.phone_number = phoneNumber;
    if (help_tips_disabled !== undefined) user.help_tips_disabled = help_tips_disabled;

    await user.save();

    // Ensure the user exists in Matrix
    try {
      console.log(`Ensuring user ${user.id} exists in Matrix after self-update...`);
      await matrixClientService.ensureMatrixUser(user.id);
      console.log(`User ${user.id} successfully created/verified in Matrix after self-update`);
    } catch (matrixError) {
      console.error('Error ensuring user exists in Matrix after self-update:', matrixError);
      // Don't fail the operation if Matrix creation fails
    }

    return res.status(200).json({
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        phoneNumber: user.phone_number,
        userType: user.user_type,
        isBusinessOwner: user.is_business_owner,
        isApproved: user.is_approved,
        twoFactorEnabled: user.two_factor_enabled,
        is_global_admin: user.is_global_admin,
        help_tips_disabled: user.help_tips_disabled
      }
    });
  } catch (error) {
    console.error('Error updating current user:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get current user (me)
export const getCurrentUser = async (req, res) => {
  try {
    const userId = req.user.id; // From auth middleware

    const user = await User.findByPk(userId, {
      attributes: { 
        exclude: ['password_hash', 'reset_password_token', 'reset_password_expires', 'two_factor_secret'] 
      }
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    return res.status(200).json({
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        phoneNumber: user.phone_number,
        userType: user.user_type,
        isBusinessOwner: user.is_business_owner,
        isApproved: user.is_approved,
        twoFactorEnabled: user.two_factor_enabled,
        is_global_admin: user.is_global_admin,
        help_tips_disabled: user.help_tips_disabled
      }
    });
  } catch (error) {
    console.error('Error getting current user:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get business users pending approval
export const getPendingApprovalUsers = async (req, res) => {
  try {
    const pendingUsers = await User.findAll({
      where: {
        is_business_owner: true,
        is_approved: false
      },
      attributes: { 
        exclude: ['password_hash', 'reset_password_token', 'reset_password_expires', 'two_factor_secret'] 
      },
      order: [['created_at', 'DESC']]
    });

    return res.status(200).json({
      users: pendingUsers.map(user => ({
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        phoneNumber: user.phone_number,
        userType: user.user_type,
        createdAt: user.created_at
      }))
    });
  } catch (error) {
    console.error('Error getting pending approval users:', error);
    return res.status(500).json({ error: error.message });
  }
};
