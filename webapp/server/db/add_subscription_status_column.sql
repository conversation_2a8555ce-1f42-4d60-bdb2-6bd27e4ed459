-- Add subscription_status column to farms table if it doesn't exist

-- Set the search path to the appropriate schema
SET search_path TO site;

ALTER TABLE site.farms ADD COLUMN IF NOT EXISTS subscription_status VARCHAR(50) NOT NULL DEFAULT 'active';

-- Add comment explaining the purpose of this column
COMMENT ON COLUMN site.farms.subscription_status IS 'Status of the subscription: active, past_due, canceled, etc.';

-- Create index for better query performance
CREATE INDEX IF NOT EXISTS idx_farms_subscription_status ON site.farms(subscription_status);
