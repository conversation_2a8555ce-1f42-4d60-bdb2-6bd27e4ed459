-- HR Feature Tables for Farm Management Platform
-- Set the search path to the appropriate schema
SET search_path TO site;

-- Time Off Requests Table
CREATE TABLE time_off_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
    request_type VARCHAR(50) NOT NULL, -- vacation, sick, personal, bereavement, etc.
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    total_days DECIMAL(5, 2) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'pending', -- pending, approved, denied, cancelled
    reason TEXT,
    notes TEXT,
    reviewed_by UUID REFERENCES users(id),
    reviewed_at TIMESTAMP,
    review_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Pay Stubs Table
CREATE TABLE pay_stubs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
    pay_period_start DATE NOT NULL,
    pay_period_end DATE NOT NULL,
    payment_date DATE NOT NULL,
    gross_pay DECIMAL(10, 2) NOT NULL,
    net_pay DECIMAL(10, 2) NOT NULL,
    regular_hours DECIMAL(10, 2),
    overtime_hours DECIMAL(10, 2),
    regular_pay DECIMAL(10, 2),
    overtime_pay DECIMAL(10, 2),
    deductions JSONB, -- JSON object containing deduction details
    taxes JSONB, -- JSON object containing tax details
    benefits JSONB, -- JSON object containing benefits details
    year_to_date JSONB, -- JSON object containing year-to-date totals
    document_url VARCHAR(255), -- URL to the pay stub document
    notes TEXT,
    status VARCHAR(50) NOT NULL DEFAULT 'issued', -- issued, viewed, etc.
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Expenses Table
CREATE TABLE expenses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
    expense_date DATE NOT NULL,
    category VARCHAR(100) NOT NULL, -- travel, meals, supplies, etc.
    amount DECIMAL(10, 2) NOT NULL,
    description TEXT NOT NULL,
    receipt_url VARCHAR(255), -- URL to the receipt document
    status VARCHAR(50) NOT NULL DEFAULT 'pending', -- pending, approved, denied, reimbursed
    payment_method VARCHAR(50), -- cash, credit card, company card, etc.
    payment_reference VARCHAR(100), -- Reference number for the payment
    notes TEXT,
    reviewed_by UUID REFERENCES users(id),
    reviewed_at TIMESTAMP,
    review_notes TEXT,
    reimbursed_date DATE,
    reimbursed_amount DECIMAL(10, 2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create triggers for updated_at timestamps
CREATE TRIGGER update_time_off_requests_timestamp BEFORE UPDATE ON time_off_requests
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_pay_stubs_timestamp BEFORE UPDATE ON pay_stubs
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_expenses_timestamp BEFORE UPDATE ON expenses
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

-- Create indexes for performance
CREATE INDEX idx_time_off_requests_employee_id ON time_off_requests(employee_id);
CREATE INDEX idx_time_off_requests_status ON time_off_requests(status);
CREATE INDEX idx_time_off_requests_start_date ON time_off_requests(start_date);
CREATE INDEX idx_time_off_requests_end_date ON time_off_requests(end_date);

CREATE INDEX idx_pay_stubs_employee_id ON pay_stubs(employee_id);
CREATE INDEX idx_pay_stubs_payment_date ON pay_stubs(payment_date);
CREATE INDEX idx_pay_stubs_pay_period_start ON pay_stubs(pay_period_start);
CREATE INDEX idx_pay_stubs_pay_period_end ON pay_stubs(pay_period_end);

CREATE INDEX idx_expenses_employee_id ON expenses(employee_id);
CREATE INDEX idx_expenses_expense_date ON expenses(expense_date);
CREATE INDEX idx_expenses_status ON expenses(status);
CREATE INDEX idx_expenses_category ON expenses(category);