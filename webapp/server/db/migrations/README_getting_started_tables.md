# Getting Started Tables Migration

This document explains the migration process for the Getting Started feature tables.

## Tables

The Getting Started feature uses two tables:

1. `getting_started_tasks` - Stores the tasks that users need to complete as part of the getting started process
2. `user_getting_started_progress` - Tracks user progress on completing the getting started tasks

## Migration Files

There are three migration files related to the Getting Started feature:

1. `create_getting_started_tables.sql` - Creates the tables if they don't exist
2. `add_getting_started_tasks.sql` - Adds sample tasks to the `getting_started_tasks` table
3. `ensure_getting_started_tasks.sql` - Ensures that the sample tasks exist in the `getting_started_tasks` table

## Running the Migrations

To set up the Getting Started feature, run the following commands in order:

1. Create the tables:
   ```
   npm run migrate:create-getting-started-tables
   ```

2. Add the sample tasks:
   ```
   npm run migrate:getting-started-tasks
   ```

## Troubleshooting

If the Getting Started widget is not displaying any content, it may be because:

1. The tables don't exist in the database
2. There are no tasks in the `getting_started_tasks` table
3. The tasks in the `getting_started_tasks` table are not active for the current user's type

To fix these issues:

1. Run the migrations as described above
2. Check the database to ensure the tables exist and contain data
3. Verify that there are active tasks for the current user's type