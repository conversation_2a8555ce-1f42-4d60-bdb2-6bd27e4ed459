-- Add missing columns to dashboard_layouts table
-- Migration to add dashboard_type and name columns to dashboard_layouts table

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Get the schema from environment variable or use 'site' as default
DO $$
DECLARE
    schema_name TEXT;
BEGIN
    -- Get the current schema or use 'site' as default
    BEGIN
        -- Check if DB_SCHEMA is set as a PostgreSQL variable with proper namespace
        SELECT current_setting('app.db_schema') INTO schema_name;
        EXCEPTION WHEN OTHERS THEN
            -- If not set, use current schema or 'site' as default
            SELECT current_schema() INTO schema_name;
    END;

    -- Add dashboard_type column if it doesn't exist
    EXECUTE format('
        ALTER TABLE %I.dashboard_layouts 
        ADD COLUMN IF NOT EXISTS dashboard_type VARCHAR(255) DEFAULT ''farm'';
    ', schema_name);

    RAISE NOTICE 'Added dashboard_type column to dashboard_layouts table in schema %', schema_name;

    -- Add name column if it doesn't exist
    EXECUTE format('
        ALTER TABLE %I.dashboard_layouts 
        ADD COLUMN IF NOT EXISTS name VARCHAR(255) DEFAULT ''Default'';
    ', schema_name);

    RAISE NOTICE 'Added name column to dashboard_layouts table in schema %', schema_name;
END
$$;
