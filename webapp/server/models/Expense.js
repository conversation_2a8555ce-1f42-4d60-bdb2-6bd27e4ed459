import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Employee from './Employee.js';
import User from './User.js';

const Expense = defineModel('Expense', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  employee_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'employees',
      key: 'id'
    }
  },
  expense_date: {
    type: DataTypes.DATE,
    allowNull: false
  },
  category: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: 'travel, meals, supplies, etc.'
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  receipt_url: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'URL to the receipt document'
  },
  status: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'pending',
    comment: 'pending, approved, denied, reimbursed'
  },
  payment_method: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'cash, credit card, company card, etc.'
  },
  payment_reference: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Reference number for the payment'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  reviewed_by: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  reviewed_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  review_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  reimbursed_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  reimbursed_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  }
}, {
  tableName: 'expenses',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

export default Expense;
