import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import User from './User.js';
import HelpTip from './HelpTip.js';
import dotenv from 'dotenv';

dotenv.config();

const UserHelpTipDismissal = defineModel('UserHelpTipDismissal', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  help_tip_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: HelpTip,
      key: 'id'
    }
  },
  dismissed_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'user_help_tip_dismissals',
  
  timestamps: true,
  createdAt: 'dismissed_at',
  updatedAt: false,
  indexes: [
    {
      unique: true,
      fields: ['user_id', 'help_tip_id']
    }
  ]
});

// Associations are defined in associations.js

export default UserHelpTipDismissal;
