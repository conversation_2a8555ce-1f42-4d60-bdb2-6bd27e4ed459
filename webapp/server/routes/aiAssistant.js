import express from 'express';
import { body } from 'express-validator';
import { authenticate } from '../middleware/index.js';
import { 
  getAIResponse, 
  getQueryHistory, 
  getSuggestions,
  getDecisionSupport,
  createDecisionSupport,
  updateDecisionSupport,
  getPredictiveMaintenance,
  createPredictiveMaintenance,
  updatePredictiveMaintenance,
  getHarvestRecommendations,
  updateHarvestRecommendation,
  getFieldImprovementRecommendations,
  updateFieldImprovementRecommendation,
  getFinancialRecommendations,
  updateFinancialRecommendation,
  getYieldProfitRecommendations,
  updateYieldProfitRecommendation
} from '../controllers/aiAssistantController.js';

const router = express.Router();

/**
 * @route POST /api/ai-assistant/query
 * @desc Get AI assistant response for a query (accessible without authentication for external API)
 * @access Public
 */
router.post(
  '/query',
  [
    body('query').notEmpty().withMessage('Query is required'),
    // Make farmId optional - will be checked in the controller
  ],
  getAIResponse
);

/**
 * @route GET /api/ai-assistant/history/:farmId
 * @desc Get AI assistant query history for a farm
 * @access Private
 */
router.get('/history/:farmId', authenticate, getQueryHistory);

/**
 * @route GET /api/ai-assistant/suggestions/:farmId
 * @desc Get AI assistant suggestions based on farm data
 * @access Private
 */
router.get('/suggestions/:farmId', authenticate, getSuggestions);

/**
 * @route GET /api/ai-assistant/decision-support/:farmId
 * @desc Get AI decision support recommendations for a farm
 * @access Private
 */
router.get('/decision-support/:farmId', authenticate, getDecisionSupport);

/**
 * @route POST /api/ai-assistant/decision-support
 * @desc Create a new AI decision support recommendation
 * @access Private
 */
router.post(
  '/decision-support',
  authenticate,
  [
    body('farmId').notEmpty().withMessage('Farm ID is required'),
    body('operationType').notEmpty().withMessage('Operation type is required'),
    body('recommendation').notEmpty().withMessage('Recommendation is required'),
    body('confidenceScore').isNumeric().withMessage('Confidence score must be a number'),
    body('factorsConsidered').notEmpty().withMessage('Factors considered are required')
  ],
  createDecisionSupport
);

/**
 * @route PUT /api/ai-assistant/decision-support/:id
 * @desc Update an AI decision support recommendation
 * @access Private
 */
router.put('/decision-support/:id', authenticate, updateDecisionSupport);

/**
 * @route GET /api/ai-assistant/predictive-maintenance/:farmId
 * @desc Get AI predictive maintenance recommendations for a farm
 * @access Private
 */
router.get('/predictive-maintenance/:farmId', authenticate, getPredictiveMaintenance);

/**
 * @route POST /api/ai-assistant/predictive-maintenance
 * @desc Create a new AI predictive maintenance recommendation
 * @access Private
 */
router.post(
  '/predictive-maintenance',
  authenticate,
  [
    body('farmId').notEmpty().withMessage('Farm ID is required'),
    body('equipmentId').notEmpty().withMessage('Equipment ID is required'),
    body('maintenanceType').notEmpty().withMessage('Maintenance type is required'),
    body('prediction').notEmpty().withMessage('Prediction is required'),
    body('urgencyLevel').notEmpty().withMessage('Urgency level is required'),
    body('confidenceScore').isNumeric().withMessage('Confidence score must be a number')
  ],
  createPredictiveMaintenance
);

/**
 * @route PUT /api/ai-assistant/predictive-maintenance/:id
 * @desc Update an AI predictive maintenance recommendation
 * @access Private
 */
router.put('/predictive-maintenance/:id', authenticate, updatePredictiveMaintenance);

/**
 * @route GET /api/ai-assistant/harvest-recommendations/:farmId
 * @desc Get AI harvest timing recommendations for a farm
 * @access Private
 */
router.get('/harvest-recommendations/:farmId', authenticate, getHarvestRecommendations);

/**
 * @route PUT /api/ai-assistant/harvest-recommendations/:id
 * @desc Update an AI harvest recommendation
 * @access Private
 */
router.put('/harvest-recommendations/:id', authenticate, updateHarvestRecommendation);

/**
 * @route GET /api/ai-assistant/field-improvement-recommendations/:farmId
 * @desc Get AI field improvement recommendations for a farm
 * @access Private
 */
router.get('/field-improvement-recommendations/:farmId', authenticate, getFieldImprovementRecommendations);

/**
 * @route PUT /api/ai-assistant/field-improvement-recommendations/:id
 * @desc Update an AI field improvement recommendation
 * @access Private
 */
router.put('/field-improvement-recommendations/:id', authenticate, updateFieldImprovementRecommendation);

/**
 * @route GET /api/ai-assistant/financial-recommendations/:farmId
 * @desc Get AI financial optimization recommendations for a farm
 * @access Private
 */
router.get('/financial-recommendations/:farmId', authenticate, getFinancialRecommendations);

/**
 * @route PUT /api/ai-assistant/financial-recommendations/:id
 * @desc Update an AI financial recommendation
 * @access Private
 */
router.put('/financial-recommendations/:id', authenticate, updateFinancialRecommendation);

/**
 * @route GET /api/ai-assistant/yield-profit-recommendations/:farmId
 * @desc Get AI yield and profit maximization recommendations for a farm
 * @access Private
 */
router.get('/yield-profit-recommendations/:farmId', authenticate, getYieldProfitRecommendations);

/**
 * @route PUT /api/ai-assistant/yield-profit-recommendations/:id
 * @desc Update an AI yield and profit maximization recommendation
 * @access Private
 */
router.put('/yield-profit-recommendations/:id', authenticate, updateYieldProfitRecommendation);

export default router;
