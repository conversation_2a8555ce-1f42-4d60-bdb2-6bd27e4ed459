import express from 'express';
import {
  getAvailableGrants,
  getAvailableLoans,
  getFinancialReports,
  applyForGrant,
  applyForLoan,
  syncFarmData
} from '../controllers/ambrookController.js';

const router = express.Router();

// Get available grants from Ambrook
// router.get('/grants', authenticate, getAvailableGrants);
router.get('/grants', getAvailableGrants);

// Get available loans from Ambrook
// router.get('/loans', authenticate, getAvailableLoans);
router.get('/loans', getAvailableLoans);

// Get financial reports from Ambrook
// router.get('/reports', authenticate, getFinancialReports);
router.get('/reports', getFinancialReports);

// Apply for a grant through Ambrook
// router.post('/grants/:grantId/apply', authenticate, applyForGrant);
router.post('/grants/:grantId/apply', applyForGrant);

// Apply for a loan through Ambrook
// router.post('/loans/:loanId/apply', authenticate, applyForLoan);
router.post('/loans/:loanId/apply', applyForLoan);

// Sync farm financial data with Ambrook
// router.post('/sync', authenticate, syncFarmData);
router.post('/sync', syncFarmData);

export default router;