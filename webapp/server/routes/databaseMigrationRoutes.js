import express from 'express';
import { 
  getAllMigrations,
  getMigration,
  applyMigration,
  skipMigration,
  scanForMigrations,
  checkMigrationStatus,
  retrySkippedMigration
} from '../controllers/databaseMigrationController.js';
import { authenticate, isGlobalAdmin } from '../middleware/authMiddleware.js';

const router = express.Router();

// Apply authentication and global admin middleware to all routes
router.use(authenticate);
router.use(isGlobalAdmin);

// Get all database migrations
router.get('/', getAllMigrations);

// Get a specific database migration by ID
router.get('/:migrationId', getMigration);

// Apply a database migration
router.post('/:migrationId/apply', applyMigration);

// Skip a database migration
router.post('/:migrationId/skip', skipMigration);

// Retry a skipped migration
router.post('/:migrationId/retry', retrySkippedMigration);

// Scan for new migrations in the db directory
router.post('/scan', scanForMigrations);

// Check if all migrations have been applied
router.get('/status/check', checkMigrationStatus);

export default router;
