import express from 'express';
import { 
  getFuelRecords, 
  getFuelRecord, 
  createFuelRecord, 
  updateFuelRecord, 
  deleteFuelRecord, 
  getFuelAnalytics 
} from '../controllers/fuelConsumptionController.js';
import { authenticateToken } from '../middleware/authMiddleware.js';
import { checkFarmAccess } from '../middleware/farmAccessMiddleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Get fuel records for a farm
router.get('/farms/:farmId/fuel-records', checkFarmAccess, getFuelRecords);

// Get a specific fuel record
router.get('/fuel-records/:recordId', checkFarmAccess, getFuelRecord);

// Create a new fuel record
router.post('/farms/:farmId/fuel-records', checkFarmAccess, createFuelRecord);

// Update a fuel record
router.put('/fuel-records/:recordId', checkFarmAccess, updateFuelRecord);

// Delete a fuel record
router.delete('/fuel-records/:recordId', checkFarmAccess, deleteFuelRecord);

// Get fuel consumption analytics for a farm
router.get('/farms/:farmId/fuel-analytics', checkFarmAccess, getFuelAnalytics);

export default router;