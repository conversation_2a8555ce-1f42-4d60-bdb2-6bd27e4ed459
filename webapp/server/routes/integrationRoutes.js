import express from 'express';
import { authenticate } from '../middleware/authMiddleware.js';
import * as integrationController from '../controllers/integrationController.js';

const router = express.Router();

// Apply authentication middleware to all integration routes
router.use(authenticate);

// Integration Routes
router.get('/', integrationController.getIntegrations);
router.get('/available', integrationController.getAvailablePlugins);
router.get('/:id', integrationController.getIntegration);
router.post('/', integrationController.createIntegration);
router.post('/install', integrationController.installPlugin);
router.put('/:id', integrationController.updateIntegration);
router.delete('/:id', integrationController.deleteIntegration);
router.put('/:id/toggle', integrationController.toggleIntegration);
router.put('/:id/settings', integrationController.updateIntegrationSettings);

export default router;