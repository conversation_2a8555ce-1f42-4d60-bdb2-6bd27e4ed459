import express from 'express';
import {
  getFarmInvoices,
  getInvoiceById,
  getFarmPaymentMethods,
  getPaymentMethod,
  getInvoiceByIdOnly,
  updatePaymentMethod,
  createPaymentMethod
} from '../controllers/paymentController.js';
import { authenticate } from '../middleware/index.js';

const router = express.Router();

// Get a single invoice by ID without requiring a farm ID (matches UUID pattern)
router.get('/invoices/:invoiceId([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})', authenticate, getInvoiceByIdOnly);

// Get a single invoice by ID for a farm
router.get('/invoices/:farmId/:invoiceId', authenticate, getInvoiceById);

// Get all invoices for a farm
router.get('/invoices/:farmId', authenticate, getFarmInvoices);

// Get a specific payment method by ID
router.get('/methods/method/:paymentMethodId', authenticate, getPaymentMethod);

// Get all payment methods for a farm
router.get('/methods/:farmId', authenticate, getFarmPaymentMethods);

// Update a payment method by ID
router.post('/methods/method/:paymentMethodId', authenticate, updatePaymentMethod);

// Create a new payment method for a farm
router.post('/methods/:farmId', authenticate, createPaymentMethod);

export default router;
