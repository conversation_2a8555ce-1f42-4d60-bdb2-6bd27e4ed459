import { sequelize } from '../config/database.js';
import { RolePermission, Farm, Role } from '../models/index.js';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';

dotenv.config();

// Get the schema from environment variables
const schema = process.env.DB_SCHEMA || 'site';

// Define the new features
const newFeatures = [
  'crop_types',
  'harvests',
  'weather_forecasts',
  'weather_alerts'
];

// Define the roles
const roles = [
  'farm_owner',
  'farm_admin',
  'farm_manager',
  'farm_employee',
  'accountant'
];

// Helper function to find or create a role by name and farm ID
const findOrCreateRoleByName = async (roleName, farmId) => {
  try {
    // First, try to find an existing role for this farm with this name
    let role = await Role.findOne({
      where: {
        name: roleName,
        farm_id: farmId
      }
    });

    // If role exists, return it
    if (role) {
      return role;
    }

    // If no farm-specific role exists, try to find a global role with this name
    role = await Role.findOne({
      where: {
        name: roleName,
        farm_id: null
      }
    });

    // If global role exists, return it
    if (role) {
      return role;
    }

    // If no role exists at all, create a new one for this farm
    console.log(`Creating new role '${roleName}' for farm ${farmId}`);
    role = await Role.create({
      name: roleName,
      farm_id: farmId,
      description: `${roleName} role for farm ${farmId}`,
      is_system_role: true
    });

    return role;
  } catch (error) {
    console.error(`Error finding or creating role '${roleName}' for farm ${farmId}:`, error);
    throw error;
  }
};

// Define default permissions for each role and feature
const getDefaultPermissions = (role, feature) => {
  // Full access for owners and admins
  if (role === 'farm_owner' || role === 'farm_admin') {
    return {
      can_view: true,
      can_create: true,
      can_edit: true,
      can_delete: true
    };
  }

  // Managers can view, create, and edit but not delete
  if (role === 'farm_manager') {
    return {
      can_view: true,
      can_create: true,
      can_edit: true,
      can_delete: false
    };
  }

  // Employees can only view
  if (role === 'farm_employee') {
    return {
      can_view: true,
      can_create: false,
      can_edit: false,
      can_delete: false
    };
  }

  // Accountants can view financial features only
  if (role === 'accountant') {
    return {
      can_view: feature.includes('weather') ? false : true,
      can_create: false,
      can_edit: false,
      can_delete: false
    };
  }

  // Default: no permissions
  return {
    can_view: false,
    can_create: false,
    can_edit: false,
    can_delete: false
  };
};

// Main function to add new role permissions
const addNewRolePermissions = async () => {
  try {
    console.log('Starting to add new role permissions...');

    // Get all farms
    const farms = await Farm.findAll();
    console.log(`Found ${farms.length} farms`);

    // For each farm, add permissions for each role and feature
    for (const farm of farms) {
      console.log(`Processing farm: ${farm.name} (${farm.id})`);

      for (const role of roles) {
        for (const feature of newFeatures) {
          // Check if permission already exists
          const existingPermission = await RolePermission.findOne({
            where: {
              farm_id: farm.id,
              role_name: role,
              feature: feature
            }
          });

          if (existingPermission) {
            console.log(`Permission for ${role} - ${feature} already exists for farm ${farm.name}`);
            continue;
          }

          // Get default permissions for this role and feature
          const permissions = getDefaultPermissions(role, feature);

          // Find or create the role to get its ID
          const roleObj = await findOrCreateRoleByName(role, farm.id);

          // Create the permission
          await RolePermission.create({
            id: uuidv4(),
            farm_id: farm.id,
            role_id: roleObj.id,
            role_name: role,
            feature: feature,
            can_view: permissions.can_view,
            can_create: permissions.can_create,
            can_edit: permissions.can_edit,
            can_delete: permissions.can_delete
          });

          console.log(`Added permission for ${role} - ${feature} for farm ${farm.name}`);
        }
      }
    }

    // Also add global permissions (farm_id = null) for system-wide roles
    for (const role of roles) {
      for (const feature of newFeatures) {
        // Check if permission already exists
        const existingPermission = await RolePermission.findOne({
          where: {
            farm_id: null,
            role_name: role,
            feature: feature
          }
        });

        if (existingPermission) {
          console.log(`Global permission for ${role} - ${feature} already exists`);
          continue;
        }

        // Get default permissions for this role and feature
        const permissions = getDefaultPermissions(role, feature);

        // Find or create the global role to get its ID
        const roleObj = await findOrCreateRoleByName(role, null);

        // Create the permission
        await RolePermission.create({
          id: uuidv4(),
          farm_id: null,
          role_id: roleObj.id,
          role_name: role,
          feature: feature,
          can_view: permissions.can_view,
          can_create: permissions.can_create,
          can_edit: permissions.can_edit,
          can_delete: permissions.can_delete
        });

        console.log(`Added global permission for ${role} - ${feature}`);
      }
    }

    console.log('Successfully added all new role permissions!');
  } catch (error) {
    console.error('Error adding new role permissions:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
};

// Run the function if this script is executed directly
if (process.argv[1] === import.meta.url) {
  addNewRolePermissions()
    .then(() => {
      console.log('Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}

export default addNewRolePermissions;
