import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function fixUserTypeColumn() {
  try {
    console.log('Starting user_type column fix...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../db/fix_user_type_column.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    await sequelize.query(sql);
    
    console.log('User type column fix completed successfully');
  } catch (error) {
    console.error('Error fixing user_type column:', error);
    process.exit(1);
  }
}

// Run the function
fixUserTypeColumn()
  .then(() => {
    console.log('Fix completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });