import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runCreateGettingStartedTablesMigration() {
  try {
    console.log('Running migration to create getting started tables...');
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, '../db/migrations/create_getting_started_tables.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Execute the SQL
    await sequelize.query(sql);
    
    console.log('Create getting started tables migration completed successfully!');
  } catch (error) {
    console.error('Error running create getting started tables migration:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

runCreateGettingStartedTablesMigration();