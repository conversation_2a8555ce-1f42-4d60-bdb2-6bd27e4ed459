const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('Running Getting Started migrations...');

try {
  // Get the project root directory
  const projectRoot = path.resolve(__dirname, '../../');
  
  // Change to the project root directory
  process.chdir(projectRoot);
  
  console.log('Creating Getting Started tables...');
  execSync('npm run migrate:create-getting-started-tables', { stdio: 'inherit' });
  
  console.log('Adding Getting Started tasks...');
  execSync('npm run migrate:getting-started-tasks', { stdio: 'inherit' });
  
  console.log('Ensuring Getting Started tasks exist...');
  execSync('npm run migrate:ensure-getting-started-tasks', { stdio: 'inherit' });
  
  console.log('Getting Started migrations completed successfully!');
} catch (error) {
  console.error('Error running Getting Started migrations:', error.message);
  process.exit(1);
}