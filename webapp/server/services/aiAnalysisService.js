import { sequelize } from '../config/database.js';
import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

/**
 * Generate a crop rotation analysis for a field or farm
 * @param {string} farmId - The farm ID
 * @param {string} fieldId - The field ID (optional)
 * @param {Object} cropData - Data about current and previous crops
 * @returns {Object} The generated crop rotation analysis
 */
export const generateCropRotationAnalysis = async (farmId, fieldId = null, cropData = {}) => {
  try {
    // Check if OpenAI API key is configured and not a placeholder
    if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'REPLACE_WITH_REAL_API_KEY') {
      console.error('OpenAI API key is not configured or is using a placeholder value');
      throw new Error('AI service is not properly configured. Please set a valid OpenAI API key.');
    }

    // Get farm information to provide context to the AI
    const farmResult = await sequelize.query(
      'SELECT name, farm_type, location FROM farms WHERE id = $1',
      { 
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (farmResult.length === 0) {
      throw new Error('Farm not found');
    }

    const farmInfo = farmResult[0];

    // Get field information if fieldId is provided
    let fieldInfo = null;
    if (fieldId) {
      const fieldResult = await sequelize.query(
        'SELECT name, acres, soil_type FROM fields WHERE id = $1 AND farm_id = $2',
        { 
          replacements: [fieldId, farmId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      if (fieldResult.length === 0) {
        throw new Error('Field not found');
      }

      fieldInfo = fieldResult[0];
    }

    // Get previous crops data if not provided
    let previousCrops = cropData.previousCrops || [];
    if (fieldId && (!previousCrops || previousCrops.length === 0)) {
      const cropsResult = await sequelize.query(
        `SELECT c.crop_name, c.planting_date, c.harvest_date, c.yield_amount, c.yield_unit
         FROM crops c
         WHERE c.field_id = $1
         ORDER BY c.planting_date DESC
         LIMIT 5`,
        { 
          replacements: [fieldId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      previousCrops = cropsResult;
    }

    // Get soil data if available
    let soilData = null;
    if (fieldId) {
      const soilResult = await sequelize.query(
        `SELECT st.soil_type, st.ph_level, st.organic_matter, st.nitrogen, st.phosphorus, st.potassium
         FROM soil_tests st
         WHERE st.field_id = $1
         ORDER BY st.test_date DESC
         LIMIT 1`,
        { 
          replacements: [fieldId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      if (soilResult.length > 0) {
        soilData = soilResult[0];
      }
    }

    // Create a prompt for generating crop rotation analysis
    const prompt = `Generate a detailed crop rotation analysis for a farm with the following details:
- Farm name: ${farmInfo.name}
- Farm type: ${farmInfo.farm_type}
- Location: ${farmInfo.location}
${fieldInfo ? `- Field name: ${fieldInfo.name}
- Field size: ${fieldInfo.acres} acres
- Soil type: ${fieldInfo.soil_type}` : ''}
${soilData ? `- Soil pH: ${soilData.ph_level}
- Organic matter: ${soilData.organic_matter}%
- Nitrogen: ${soilData.nitrogen} ppm
- Phosphorus: ${soilData.phosphorus} ppm
- Potassium: ${soilData.potassium} ppm` : ''}

Current crop: ${cropData.currentCrop || 'Unknown'}

Previous crops:
${previousCrops.length > 0 ? 
  previousCrops.map(crop => `- ${crop.crop_name} (${crop.planting_date ? new Date(crop.planting_date).getFullYear() : 'Unknown year'})`).join('\n') : 
  'No previous crop data available'}

Based on this information, provide:
1. A list of recommended next crops with reasons for each recommendation
2. A multi-year rotation plan
3. The impact of this rotation plan on soil health
4. The impact on pest management
5. The expected impact on yield

Format your response as a JSON object with the following structure:
{
  "recommended_next_crops": [
    {"crop": "Crop Name", "reason": "Reason for recommendation"}
  ],
  "rotation_plan": {
    "year1": "Crop for year 1",
    "year2": "Crop for year 2",
    "year3": "Crop for year 3",
    "year4": "Crop for year 4"
  },
  "soil_health_impact": "Description of impact on soil health",
  "pest_management_impact": "Description of impact on pest management",
  "yield_impact": "Description of expected impact on yield",
  "confidence_score": 85.5
}`;

    // Call OpenAI API to generate analysis
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        { role: "system", content: "You are an AI farming assistant that provides data-driven crop rotation analyses." },
        { role: "user", content: prompt }
      ],
      max_tokens: 1000,
      temperature: 0.7,
      response_format: { type: "json_object" }
    });

    // Parse the response
    const aiResponse = completion.choices[0].message.content.trim();
    const analysis = JSON.parse(aiResponse);

    // Store the analysis in the database
    const [result] = await sequelize.query(
      `INSERT INTO ai_crop_rotation_analyses (
        farm_id, 
        field_id, 
        current_crop, 
        previous_crops, 
        recommended_next_crops, 
        rotation_plan, 
        soil_health_impact, 
        pest_management_impact, 
        yield_impact, 
        confidence_score
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *`,
      { 
        replacements: [
          farmId,
          fieldId,
          cropData.currentCrop || null,
          JSON.stringify(previousCrops),
          JSON.stringify(analysis.recommended_next_crops),
          JSON.stringify(analysis.rotation_plan),
          analysis.soil_health_impact,
          analysis.pest_management_impact,
          analysis.yield_impact,
          analysis.confidence_score || 85.0
        ],
        type: sequelize.QueryTypes.SELECT
      }
    );

    return result;
  } catch (error) {
    console.error('Error generating crop rotation analysis:', error);

    // Create a fallback analysis if AI generation fails
    const fallbackAnalysis = {
      farm_id: farmId,
      field_id: fieldId,
      current_crop: cropData.currentCrop || null,
      previous_crops: JSON.stringify(cropData.previousCrops || []),
      recommended_next_crops: JSON.stringify([
        { crop: "Legumes (e.g., Soybeans, Alfalfa)", reason: "To fix nitrogen in the soil and break pest cycles" },
        { crop: "Small Grains (e.g., Wheat, Barley)", reason: "To add organic matter and reduce soil erosion" },
        { crop: "Cover Crops (e.g., Clover, Rye)", reason: "To improve soil structure and suppress weeds" }
      ]),
      rotation_plan: JSON.stringify({
        year1: "Corn",
        year2: "Soybeans",
        year3: "Wheat",
        year4: "Alfalfa"
      }),
      soil_health_impact: "This rotation will improve soil structure, increase organic matter, and enhance nutrient cycling.",
      pest_management_impact: "Breaking crop cycles will disrupt pest life cycles and reduce pest pressure.",
      yield_impact: "Expected 10-15% yield increase over continuous cropping systems.",
      confidence_score: 75.0,
      analysis_date: new Date(),
      is_applied: false
    };

    try {
      // Store the fallback analysis in the database
      const [result] = await sequelize.query(
        `INSERT INTO ai_crop_rotation_analyses (
          farm_id, 
          field_id, 
          current_crop, 
          previous_crops, 
          recommended_next_crops, 
          rotation_plan, 
          soil_health_impact, 
          pest_management_impact, 
          yield_impact, 
          confidence_score
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING *`,
        { 
          replacements: [
            fallbackAnalysis.farm_id,
            fallbackAnalysis.field_id,
            fallbackAnalysis.current_crop,
            fallbackAnalysis.previous_crops,
            fallbackAnalysis.recommended_next_crops,
            fallbackAnalysis.rotation_plan,
            fallbackAnalysis.soil_health_impact,
            fallbackAnalysis.pest_management_impact,
            fallbackAnalysis.yield_impact,
            fallbackAnalysis.confidence_score
          ],
          type: sequelize.QueryTypes.SELECT
        }
      );

      return result;
    } catch (dbError) {
      console.error('Error storing fallback crop rotation analysis:', dbError);
      throw new Error('Failed to generate and store crop rotation analysis');
    }
  }
};

/**
 * Get the latest crop rotation analysis for a field or farm
 * @param {string} farmId - The farm ID
 * @param {string} fieldId - The field ID (optional)
 * @returns {Object} The latest crop rotation analysis
 */
export const getLatestCropRotationAnalysis = async (farmId, fieldId = null) => {
  try {
    let query = `
      SELECT * FROM ai_crop_rotation_analyses 
      WHERE farm_id = $1 
      ${fieldId ? 'AND field_id = $2' : 'AND field_id IS NULL'} 
      ORDER BY analysis_date DESC 
      LIMIT 1
    `;

    const replacements = fieldId ? [farmId, fieldId] : [farmId];

    const result = await sequelize.query(query, { 
      replacements,
      type: sequelize.QueryTypes.SELECT
    });

    if (result.length === 0) {
      // No analysis found, generate a new one
      return await generateCropRotationAnalysis(farmId, fieldId);
    }

    return result[0];
  } catch (error) {
    console.error('Error getting latest crop rotation analysis:', error);
    throw new Error('Failed to retrieve crop rotation analysis');
  }
};

/**
 * Apply a crop rotation analysis to farm planning
 * @param {string} analysisId - The analysis ID
 * @param {boolean} isApplied - Whether the analysis is applied
 * @returns {Object} The updated analysis
 */
export const applyCropRotationAnalysis = async (analysisId, isApplied = true) => {
  try {
    const [result] = await sequelize.query(
      `UPDATE ai_crop_rotation_analyses 
       SET is_applied = $1, updated_at = CURRENT_TIMESTAMP 
       WHERE id = $2 
       RETURNING *`,
      { 
        replacements: [isApplied, analysisId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (!result) {
      throw new Error('Analysis not found');
    }

    return result;
  } catch (error) {
    console.error('Error applying crop rotation analysis:', error);
    throw new Error('Failed to update crop rotation analysis');
  }
};

/**
 * Get all crop rotation analyses for a farm
 * @param {string} farmId - The farm ID
 * @returns {Array} The crop rotation analyses
 */
export const getAllCropRotationAnalyses = async (farmId) => {
  try {
    // First check if the farm exists
    const farmExists = await sequelize.query(
      'SELECT 1 FROM farms WHERE id = $1',
      {
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (farmExists.length === 0) {
      throw new Error(`Farm with ID ${farmId} not found`);
    }

    const result = await sequelize.query(
      `SELECT a.*, f.name as field_name 
       FROM ai_crop_rotation_analyses a
       LEFT JOIN fields f ON a.field_id = f.id
       WHERE a.farm_id = $1 
       ORDER BY a.analysis_date DESC`,
      { 
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    return result;
  } catch (error) {
    console.error('Error getting all crop rotation analyses:', error);
    throw new Error(`Failed to retrieve crop rotation analyses: ${error.message}`);
  }
};

/**
 * Generate a harvest schedule analysis for a field or farm
 * @param {string} farmId - The farm ID
 * @param {string} fieldId - The field ID (optional)
 * @param {string} cropId - The crop ID (optional)
 * @param {Object} harvestData - Data about planting and crop maturity
 * @returns {Object} The generated harvest schedule analysis
 */
export const generateHarvestScheduleAnalysis = async (farmId, fieldId = null, cropId = null, harvestData = {}) => {
  try {
    // Check if OpenAI API key is configured and not a placeholder
    if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'REPLACE_WITH_REAL_API_KEY') {
      console.error('OpenAI API key is not configured or is using a placeholder value');
      throw new Error('AI service is not properly configured. Please set a valid OpenAI API key.');
    }

    // Get farm information to provide context to the AI
    const farmResult = await sequelize.query(
      'SELECT name, farm_type, location FROM farms WHERE id = $1',
      { 
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (farmResult.length === 0) {
      throw new Error('Farm not found');
    }

    const farmInfo = farmResult[0];

    // Get field information if fieldId is provided
    let fieldInfo = null;
    if (fieldId) {
      const fieldResult = await sequelize.query(
        'SELECT name, acres, soil_type FROM fields WHERE id = $1 AND farm_id = $2',
        { 
          replacements: [fieldId, farmId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      if (fieldResult.length === 0) {
        throw new Error('Field not found');
      }

      fieldInfo = fieldResult[0];
    }

    // Get crop information if cropId is provided
    let cropInfo = null;
    if (cropId) {
      const cropResult = await sequelize.query(
        'SELECT crop_name, planting_date, expected_harvest_date, variety FROM crops WHERE id = $1',
        { 
          replacements: [cropId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      if (cropResult.length === 0) {
        throw new Error('Crop not found');
      }

      cropInfo = cropResult[0];
    }

    // Get weather forecast data
    let weatherData = null;
    try {
      const weatherResult = await sequelize.query(
        `SELECT forecast_date, high_temp, low_temp, precipitation, conditions
         FROM weather_forecasts
         WHERE location_name = $1
         AND forecast_date >= CURRENT_DATE
         ORDER BY forecast_date ASC
         LIMIT 10`,
        { 
          replacements: [farmInfo.location],
          type: sequelize.QueryTypes.SELECT
        }
      );

      if (weatherResult.length > 0) {
        weatherData = weatherResult;
      }
    } catch (weatherError) {
      console.error('Error fetching weather data:', weatherError);
      // Continue without weather data
    }

    // Get equipment availability data
    let equipmentData = null;
    try {
      const equipmentResult = await sequelize.query(
        `SELECT e.name, e.type, m.scheduled_date, m.duration_hours
         FROM equipment e
         LEFT JOIN maintenance_schedules m ON e.id = m.equipment_id
         WHERE e.farm_id = $1 AND e.type = 'Harvester'
         AND (m.scheduled_date IS NULL OR m.scheduled_date >= CURRENT_DATE)
         ORDER BY e.name ASC`,
        { 
          replacements: [farmId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      if (equipmentResult.length > 0) {
        equipmentData = equipmentResult;
      }
    } catch (equipmentError) {
      console.error('Error fetching equipment data:', equipmentError);
      // Continue without equipment data
    }

    // Create a prompt for generating harvest schedule analysis
    const prompt = `Generate a detailed harvest schedule analysis for a farm with the following details:
- Farm name: ${farmInfo.name}
- Farm type: ${farmInfo.farm_type}
- Location: ${farmInfo.location}
${fieldInfo ? `- Field name: ${fieldInfo.name}
- Field size: ${fieldInfo.acres} acres
- Soil type: ${fieldInfo.soil_type}` : ''}
${cropInfo ? `- Crop: ${cropInfo.crop_name}
- Variety: ${cropInfo.variety || 'Unknown'}
- Planting date: ${cropInfo.planting_date ? new Date(cropInfo.planting_date).toISOString().split('T')[0] : 'Unknown'}
- Expected harvest date: ${cropInfo.expected_harvest_date ? new Date(cropInfo.expected_harvest_date).toISOString().split('T')[0] : 'Unknown'}` : ''}
${harvestData.plantingDate ? `- Planting date: ${harvestData.plantingDate}` : ''}
${harvestData.cropMaturityData ? `- Crop maturity data: ${JSON.stringify(harvestData.cropMaturityData)}` : ''}

Weather forecast:
${weatherData ? weatherData.map(day => 
  `- ${new Date(day.forecast_date).toISOString().split('T')[0]}: High ${day.high_temp}°F, Low ${day.low_temp}°F, ${day.conditions}, Precipitation: ${day.precipitation}"`
).join('\n') : 'No weather forecast data available'}

Equipment availability:
${equipmentData ? equipmentData.map(eq => 
  `- ${eq.name} (${eq.type})${eq.scheduled_date ? ` - Maintenance scheduled on ${new Date(eq.scheduled_date).toISOString().split('T')[0]} for ${eq.duration_hours} hours` : ''}`
).join('\n') : 'No equipment availability data available'}

Based on this information, provide:
1. A recommended harvest window (start and end dates)
2. An optimal harvest date
3. Weather considerations for harvest timing
4. Impact of equipment availability on harvest timing
5. Impact of harvest timing on crop quality
6. Impact of harvest timing on yield

Format your response as a JSON object with the following structure:
{
  "recommended_harvest_window": {
    "start_date": "YYYY-MM-DD",
    "end_date": "YYYY-MM-DD"
  },
  "optimal_harvest_date": "YYYY-MM-DD",
  "weather_considerations": "Description of weather considerations",
  "equipment_availability_impact": "Description of equipment availability impact",
  "quality_impact": "Description of impact on crop quality",
  "yield_impact": "Description of impact on yield",
  "confidence_score": 85.5
}`;

    // Call OpenAI API to generate analysis
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        { role: "system", content: "You are an AI farming assistant that provides data-driven harvest scheduling analyses." },
        { role: "user", content: prompt }
      ],
      max_tokens: 1000,
      temperature: 0.7,
      response_format: { type: "json_object" }
    });

    // Parse the response
    const aiResponse = completion.choices[0].message.content.trim();
    const analysis = JSON.parse(aiResponse);

    // Store the analysis in the database
    const [result] = await sequelize.query(
      `INSERT INTO ai_harvest_schedule_analyses (
        farm_id, 
        field_id, 
        crop_id, 
        planting_date, 
        crop_maturity_data, 
        recommended_harvest_window, 
        optimal_harvest_date, 
        weather_considerations, 
        equipment_availability_impact, 
        quality_impact, 
        yield_impact, 
        confidence_score
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      RETURNING *`,
      { 
        replacements: [
          farmId,
          fieldId,
          cropId,
          harvestData.plantingDate || (cropInfo ? cropInfo.planting_date : null),
          JSON.stringify(harvestData.cropMaturityData || {}),
          JSON.stringify(analysis.recommended_harvest_window),
          analysis.optimal_harvest_date,
          analysis.weather_considerations,
          analysis.equipment_availability_impact,
          analysis.quality_impact,
          analysis.yield_impact,
          analysis.confidence_score || 85.0
        ],
        type: sequelize.QueryTypes.SELECT
      }
    );

    return result;
  } catch (error) {
    console.error('Error generating harvest schedule analysis:', error);

    // Create a fallback analysis if AI generation fails
    const fallbackAnalysis = {
      farm_id: farmId,
      field_id: fieldId,
      crop_id: cropId,
      planting_date: harvestData.plantingDate || null,
      crop_maturity_data: JSON.stringify(harvestData.cropMaturityData || {}),
      recommended_harvest_window: JSON.stringify({
        start_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        end_date: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      }),
      optimal_harvest_date: new Date(Date.now() + 38 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      weather_considerations: "Monitor weather forecasts for precipitation events that could delay harvest operations.",
      equipment_availability_impact: "Ensure harvesting equipment is properly maintained and available during the recommended harvest window.",
      quality_impact: "Harvesting at optimal moisture content will maximize quality and minimize post-harvest losses.",
      yield_impact: "Timely harvest will minimize yield losses from shattering, lodging, or disease development.",
      confidence_score: 75.0,
      analysis_date: new Date(),
      is_applied: false
    };

    try {
      // Store the fallback analysis in the database
      const [result] = await sequelize.query(
        `INSERT INTO ai_harvest_schedule_analyses (
          farm_id, 
          field_id, 
          crop_id, 
          planting_date, 
          crop_maturity_data, 
          recommended_harvest_window, 
          optimal_harvest_date, 
          weather_considerations, 
          equipment_availability_impact, 
          quality_impact, 
          yield_impact, 
          confidence_score
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        RETURNING *`,
        { 
          replacements: [
            fallbackAnalysis.farm_id,
            fallbackAnalysis.field_id,
            fallbackAnalysis.crop_id,
            fallbackAnalysis.planting_date,
            fallbackAnalysis.crop_maturity_data,
            fallbackAnalysis.recommended_harvest_window,
            fallbackAnalysis.optimal_harvest_date,
            fallbackAnalysis.weather_considerations,
            fallbackAnalysis.equipment_availability_impact,
            fallbackAnalysis.quality_impact,
            fallbackAnalysis.yield_impact,
            fallbackAnalysis.confidence_score
          ],
          type: sequelize.QueryTypes.SELECT
        }
      );

      return result;
    } catch (dbError) {
      console.error('Error storing fallback harvest schedule analysis:', dbError);
      throw new Error('Failed to generate and store harvest schedule analysis');
    }
  }
};

/**
 * Get the latest harvest schedule analysis for a field or farm
 * @param {string} farmId - The farm ID
 * @param {string} fieldId - The field ID (optional)
 * @param {string} cropId - The crop ID (optional)
 * @returns {Object} The latest harvest schedule analysis
 */
export const getLatestHarvestScheduleAnalysis = async (farmId, fieldId = null, cropId = null) => {
  try {
    let query = `
      SELECT a.*, f.name as field_name, c.crop_name 
      FROM ai_harvest_schedule_analyses a
      LEFT JOIN fields f ON a.field_id = f.id
      LEFT JOIN crops c ON a.crop_id = c.id
      WHERE a.farm_id = $1 
    `;

    const replacements = [farmId];
    let paramIndex = 2;

    if (fieldId) {
      query += ` AND a.field_id = $${paramIndex}`;
      replacements.push(fieldId);
      paramIndex++;
    } else {
      query += ` AND a.field_id IS NULL`;
    }

    if (cropId) {
      query += ` AND a.crop_id = $${paramIndex}`;
      replacements.push(cropId);
    }

    query += ` ORDER BY a.analysis_date DESC LIMIT 1`;

    const result = await sequelize.query(query, { 
      replacements,
      type: sequelize.QueryTypes.SELECT
    });

    if (result.length === 0) {
      // No analysis found, generate a new one
      return await generateHarvestScheduleAnalysis(farmId, fieldId, cropId);
    }

    return result[0];
  } catch (error) {
    console.error('Error getting latest harvest schedule analysis:', error);
    throw new Error('Failed to retrieve harvest schedule analysis');
  }
};

/**
 * Apply a harvest schedule analysis to farm planning
 * @param {string} analysisId - The analysis ID
 * @param {boolean} isApplied - Whether the analysis is applied
 * @returns {Object} The updated analysis
 */
export const applyHarvestScheduleAnalysis = async (analysisId, isApplied = true) => {
  try {
    const [result] = await sequelize.query(
      `UPDATE ai_harvest_schedule_analyses 
       SET is_applied = $1, updated_at = CURRENT_TIMESTAMP 
       WHERE id = $2 
       RETURNING *`,
      { 
        replacements: [isApplied, analysisId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (!result) {
      throw new Error('Analysis not found');
    }

    return result;
  } catch (error) {
    console.error('Error applying harvest schedule analysis:', error);
    throw new Error('Failed to update harvest schedule analysis');
  }
};

/**
 * Get all harvest schedule analyses for a farm
 * @param {string} farmId - The farm ID
 * @returns {Array} The harvest schedule analyses
 */
export const getAllHarvestScheduleAnalyses = async (farmId) => {
  try {
    // First check if the farm exists
    const farmExists = await sequelize.query(
      'SELECT 1 FROM farms WHERE id = $1',
      {
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (farmExists.length === 0) {
      throw new Error(`Farm with ID ${farmId} not found`);
    }

    const result = await sequelize.query(
      `SELECT a.*, f.name as field_name, c.crop_name 
       FROM ai_harvest_schedule_analyses a
       LEFT JOIN fields f ON a.field_id = f.id
       LEFT JOIN crops c ON a.crop_id = c.id
       WHERE a.farm_id = $1 
       ORDER BY a.analysis_date DESC`,
      { 
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    return result;
  } catch (error) {
    console.error('Error getting all harvest schedule analyses:', error);
    throw new Error(`Failed to retrieve harvest schedule analyses: ${error.message}`);
  }
};

/**
 * Generate a soil health analysis for a field or farm
 * @param {string} farmId - The farm ID
 * @param {string} fieldId - The field ID (optional)
 * @param {Object} soilData - Additional soil data (optional)
 * @returns {Object} The generated soil health analysis
 */
export const generateSoilHealthAnalysis = async (farmId, fieldId = null, soilData = {}) => {
  try {
    // Check if OpenAI API key is configured and not a placeholder
    if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'REPLACE_WITH_REAL_API_KEY') {
      console.error('OpenAI API key is not configured or is using a placeholder value');
      throw new Error('AI service is not properly configured. Please set a valid OpenAI API key.');
    }

    // Get farm information to provide context to the AI
    const farmResult = await sequelize.query(
      'SELECT name, farm_type, location FROM farms WHERE id = $1',
      { 
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (farmResult.length === 0) {
      throw new Error('Farm not found');
    }

    const farmInfo = farmResult[0];

    // Get field information if fieldId is provided
    let fieldInfo = null;
    if (fieldId) {
      const fieldResult = await sequelize.query(
        'SELECT name, acres, soil_type FROM fields WHERE id = $1 AND farm_id = $2',
        { 
          replacements: [fieldId, farmId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      if (fieldResult.length === 0) {
        throw new Error('Field not found');
      }

      fieldInfo = fieldResult[0];
    }

    // Get soil test data if available
    let soilTestData = null;
    if (fieldId) {
      const soilTestResult = await sequelize.query(
        `SELECT st.*, f.name as field_name
         FROM soil_tests st
         JOIN fields f ON st.field_id = f.id
         WHERE st.field_id = $1
         ORDER BY st.test_date DESC
         LIMIT 3`,
        { 
          replacements: [fieldId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      if (soilTestResult.length > 0) {
        soilTestData = soilTestResult;
      }
    }

    // Get crop history for the field
    let cropHistory = null;
    if (fieldId) {
      const cropHistoryResult = await sequelize.query(
        `SELECT c.crop_name, c.planting_date, c.harvest_date, c.yield_amount, c.yield_unit
         FROM crops c
         WHERE c.field_id = $1
         ORDER BY c.planting_date DESC
         LIMIT 5`,
        { 
          replacements: [fieldId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      if (cropHistoryResult.length > 0) {
        cropHistory = cropHistoryResult;
      }
    }

    // Create a prompt for generating soil health analysis
    const prompt = `Generate a detailed soil health analysis for a farm with the following details:
- Farm name: ${farmInfo.name}
- Farm type: ${farmInfo.farm_type}
- Location: ${farmInfo.location}
${fieldInfo ? `- Field name: ${fieldInfo.name}
- Field size: ${fieldInfo.acres} acres
- Soil type: ${fieldInfo.soil_type}` : ''}

${soilTestData ? `Soil test data:
${soilTestData.map(test => `
Test date: ${new Date(test.test_date).toISOString().split('T')[0]}
pH: ${test.ph_level}
Organic matter: ${test.organic_matter}%
Nitrogen: ${test.nitrogen} ppm
Phosphorus: ${test.phosphorus} ppm
Potassium: ${test.potassium} ppm
${test.other_nutrients ? `Other nutrients: ${test.other_nutrients}` : ''}
`).join('\n')}` : 'No soil test data available'}

${cropHistory ? `Crop history:
${cropHistory.map(crop => `- ${crop.crop_name} (${crop.planting_date ? new Date(crop.planting_date).toISOString().split('T')[0] : 'Unknown'} to ${crop.harvest_date ? new Date(crop.harvest_date).toISOString().split('T')[0] : 'Unknown'})`).join('\n')}` : 'No crop history available'}

${soilData.notes ? `Additional notes: ${soilData.notes}` : ''}

Based on this information, provide:
1. Current nutrient levels assessment
2. Soil pH assessment
3. Organic matter assessment
4. Identified soil health issues
5. Specific recommendations for improving soil health
6. Expected improvements from implementing recommendations
7. Implementation timeline

Format your response as a JSON object with the following structure:
{
  "nutrient_levels": {
    "nitrogen": "Low/Medium/High",
    "phosphorus": "Low/Medium/High",
    "potassium": "Low/Medium/High",
    "other_nutrients": "Description of other nutrient levels"
  },
  "ph_level": "Acidic/Neutral/Alkaline",
  "organic_matter": "Low/Medium/High",
  "issues_identified": [
    {"issue": "Issue name", "description": "Issue description"}
  ],
  "recommendations": [
    {"action": "Recommended action", "details": "Details of the recommendation"}
  ],
  "expected_improvements": "Description of expected improvements",
  "implementation_timeline": "Suggested timeline for implementation",
  "confidence_score": 85.5
}`;

    // Call OpenAI API to generate analysis
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        { role: "system", content: "You are an AI farming assistant that provides data-driven soil health analyses." },
        { role: "user", content: prompt }
      ],
      max_tokens: 1000,
      temperature: 0.7,
      response_format: { type: "json_object" }
    });

    // Parse the response
    const aiResponse = completion.choices[0].message.content.trim();
    const analysis = JSON.parse(aiResponse);

    // Store the analysis in the database
    const [result] = await sequelize.query(
      `INSERT INTO ai_soil_health_analyses (
        farm_id, 
        field_id, 
        soil_test_data, 
        nutrient_levels, 
        organic_matter, 
        ph_level, 
        issues_identified, 
        recommendations, 
        expected_improvements, 
        implementation_timeline, 
        confidence_score
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *`,
      { 
        replacements: [
          farmId,
          fieldId,
          JSON.stringify(soilTestData || {}),
          JSON.stringify(analysis.nutrient_levels),
          analysis.organic_matter === 'Low' ? 1.5 : analysis.organic_matter === 'Medium' ? 3.0 : 4.5,
          analysis.ph_level === 'Acidic' ? 5.5 : analysis.ph_level === 'Neutral' ? 7.0 : 8.5,
          JSON.stringify(analysis.issues_identified),
          JSON.stringify(analysis.recommendations),
          analysis.expected_improvements,
          analysis.implementation_timeline,
          analysis.confidence_score || 85.0
        ],
        type: sequelize.QueryTypes.SELECT
      }
    );

    return result;
  } catch (error) {
    console.error('Error generating soil health analysis:', error);

    // Create a fallback analysis if AI generation fails
    const fallbackAnalysis = {
      farm_id: farmId,
      field_id: fieldId,
      soil_test_data: JSON.stringify(soilData.soilTestData || {}),
      nutrient_levels: JSON.stringify({
        nitrogen: "Medium",
        phosphorus: "Low",
        potassium: "Medium",
        other_nutrients: "Calcium and magnesium levels are adequate."
      }),
      organic_matter: 3.0,
      ph_level: 6.5,
      issues_identified: JSON.stringify([
        { issue: "Low phosphorus", description: "Phosphorus levels are below optimal range for most crops." },
        { issue: "Moderate compaction", description: "Soil shows signs of compaction which may restrict root growth." }
      ]),
      recommendations: JSON.stringify([
        { action: "Apply phosphorus fertilizer", details: "Apply 40-60 lbs/acre of P2O5 before planting." },
        { action: "Incorporate cover crops", details: "Plant deep-rooted cover crops like radishes to reduce compaction." },
        { action: "Add organic matter", details: "Apply 2-3 tons/acre of compost to improve soil structure and nutrient retention." }
      ]),
      expected_improvements: "Improved nutrient availability, better soil structure, increased water infiltration, and enhanced microbial activity.",
      implementation_timeline: "Begin phosphorus application immediately. Plant cover crops after harvest. Add compost in fall or early spring.",
      confidence_score: 75.0,
      analysis_date: new Date(),
      is_applied: false
    };

    try {
      // Store the fallback analysis in the database
      const [result] = await sequelize.query(
        `INSERT INTO ai_soil_health_analyses (
          farm_id, 
          field_id, 
          soil_test_data, 
          nutrient_levels, 
          organic_matter, 
          ph_level, 
          issues_identified, 
          recommendations, 
          expected_improvements, 
          implementation_timeline, 
          confidence_score
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        RETURNING *`,
        { 
          replacements: [
            fallbackAnalysis.farm_id,
            fallbackAnalysis.field_id,
            fallbackAnalysis.soil_test_data,
            fallbackAnalysis.nutrient_levels,
            fallbackAnalysis.organic_matter,
            fallbackAnalysis.ph_level,
            fallbackAnalysis.issues_identified,
            fallbackAnalysis.recommendations,
            fallbackAnalysis.expected_improvements,
            fallbackAnalysis.implementation_timeline,
            fallbackAnalysis.confidence_score
          ],
          type: sequelize.QueryTypes.SELECT
        }
      );

      return result;
    } catch (dbError) {
      console.error('Error storing fallback soil health analysis:', dbError);
      throw new Error('Failed to generate and store soil health analysis');
    }
  }
};

/**
 * Get the latest soil health analysis for a field or farm
 * @param {string} farmId - The farm ID
 * @param {string} fieldId - The field ID (optional)
 * @returns {Object} The latest soil health analysis
 */
export const getLatestSoilHealthAnalysis = async (farmId, fieldId = null) => {
  try {
    let query = `
      SELECT a.*, f.name as field_name 
      FROM ai_soil_health_analyses a
      LEFT JOIN fields f ON a.field_id = f.id
      WHERE a.farm_id = $1 
      ${fieldId ? 'AND a.field_id = $2' : 'AND a.field_id IS NULL'} 
      ORDER BY a.analysis_date DESC 
      LIMIT 1
    `;

    const replacements = fieldId ? [farmId, fieldId] : [farmId];

    const result = await sequelize.query(query, { 
      replacements,
      type: sequelize.QueryTypes.SELECT
    });

    if (result.length === 0) {
      // No analysis found, generate a new one
      return await generateSoilHealthAnalysis(farmId, fieldId);
    }

    return result[0];
  } catch (error) {
    console.error('Error getting latest soil health analysis:', error);
    throw new Error('Failed to retrieve soil health analysis');
  }
};

/**
 * Apply a soil health analysis to farm planning
 * @param {string} analysisId - The analysis ID
 * @param {boolean} isApplied - Whether the analysis is applied
 * @returns {Object} The updated analysis
 */
export const applySoilHealthAnalysis = async (analysisId, isApplied = true) => {
  try {
    const [result] = await sequelize.query(
      `UPDATE ai_soil_health_analyses 
       SET is_applied = $1, updated_at = CURRENT_TIMESTAMP 
       WHERE id = $2 
       RETURNING *`,
      { 
        replacements: [isApplied, analysisId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (!result) {
      throw new Error('Analysis not found');
    }

    return result;
  } catch (error) {
    console.error('Error applying soil health analysis:', error);
    throw new Error('Failed to update soil health analysis');
  }
};

/**
 * Get all soil health analyses for a farm
 * @param {string} farmId - The farm ID
 * @returns {Array} The soil health analyses
 */
export const getAllSoilHealthAnalyses = async (farmId) => {
  try {
    // First check if the farm exists
    const farmExists = await sequelize.query(
      'SELECT 1 FROM farms WHERE id = $1',
      {
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (farmExists.length === 0) {
      throw new Error(`Farm with ID ${farmId} not found`);
    }

    const result = await sequelize.query(
      `SELECT a.*, f.name as field_name 
       FROM ai_soil_health_analyses a
       LEFT JOIN fields f ON a.field_id = f.id
       WHERE a.farm_id = $1 
       ORDER BY a.analysis_date DESC`,
      { 
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    return result;
  } catch (error) {
    console.error('Error getting all soil health analyses:', error);
    throw new Error(`Failed to retrieve soil health analyses: ${error.message}`);
  }
};

/**
 * Generate a field health analysis for a field or farm
 * @param {string} farmId - The farm ID
 * @param {string} fieldId - The field ID (optional)
 * @param {string} cropId - The crop ID (optional)
 * @param {Object} fieldData - Additional field data (optional)
 * @returns {Object} The generated field health analysis
 */
export const generateFieldHealthAnalysis = async (farmId, fieldId = null, cropId = null, fieldData = {}) => {
  try {
    // Check if OpenAI API key is configured and not a placeholder
    if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'REPLACE_WITH_REAL_API_KEY') {
      console.error('OpenAI API key is not configured or is using a placeholder value');
      throw new Error('AI service is not properly configured. Please set a valid OpenAI API key.');
    }

    // Get farm information to provide context to the AI
    const farmResult = await sequelize.query(
      'SELECT name, farm_type, location FROM farms WHERE id = $1',
      { 
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (farmResult.length === 0) {
      throw new Error('Farm not found');
    }

    const farmInfo = farmResult[0];

    // Get field information if fieldId is provided
    let fieldInfo = null;
    if (fieldId) {
      const fieldResult = await sequelize.query(
        'SELECT name, acres, soil_type FROM fields WHERE id = $1 AND farm_id = $2',
        { 
          replacements: [fieldId, farmId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      if (fieldResult.length === 0) {
        throw new Error('Field not found');
      }

      fieldInfo = fieldResult[0];
    }

    // Get crop information if cropId is provided
    let cropInfo = null;
    if (cropId) {
      const cropResult = await sequelize.query(
        'SELECT crop_name, planting_date, expected_harvest_date, variety FROM crops WHERE id = $1',
        { 
          replacements: [cropId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      if (cropResult.length === 0) {
        throw new Error('Crop not found');
      }

      cropInfo = cropResult[0];
    }

    // Get vegetation index data if available
    let vegetationData = null;
    if (fieldId) {
      try {
        const vegetationResult = await sequelize.query(
          `SELECT date, ndvi, evi, savi FROM vegetation_indices
           WHERE field_id = $1
           ORDER BY date DESC
           LIMIT 5`,
          { 
            replacements: [fieldId],
            type: sequelize.QueryTypes.SELECT
          }
        );

        if (vegetationResult.length > 0) {
          vegetationData = vegetationResult;
        }
      } catch (error) {
        console.error('Error fetching vegetation data:', error);
        // Continue without vegetation data
      }
    }

    // Get pest pressure data if available
    let pestData = null;
    if (fieldId) {
      try {
        const pestResult = await sequelize.query(
          `SELECT date, pest_name, severity, affected_area FROM pest_observations
           WHERE field_id = $1
           ORDER BY date DESC
           LIMIT 5`,
          { 
            replacements: [fieldId],
            type: sequelize.QueryTypes.SELECT
          }
        );

        if (pestResult.length > 0) {
          pestData = pestResult;
        }
      } catch (error) {
        console.error('Error fetching pest data:', error);
        // Continue without pest data
      }
    }

    // Get weather data
    let weatherData = null;
    try {
      const weatherResult = await sequelize.query(
        `SELECT date, precipitation, temperature_high, temperature_low, humidity FROM weather_data
         WHERE location = $1
         ORDER BY date DESC
         LIMIT 10`,
        { 
          replacements: [farmInfo.location],
          type: sequelize.QueryTypes.SELECT
        }
      );

      if (weatherResult.length > 0) {
        weatherData = weatherResult;
      }
    } catch (error) {
      console.error('Error fetching weather data:', error);
      // Continue without weather data
    }

    // Create a prompt for generating field health analysis
    const prompt = `Generate a detailed field health analysis for a farm with the following details:
- Farm name: ${farmInfo.name}
- Farm type: ${farmInfo.farm_type}
- Location: ${farmInfo.location}
${fieldInfo ? `- Field name: ${fieldInfo.name}
- Field size: ${fieldInfo.acres} acres
- Soil type: ${fieldInfo.soil_type}` : ''}
${cropInfo ? `- Crop: ${cropInfo.crop_name}
- Variety: ${cropInfo.variety || 'Unknown'}
- Planting date: ${cropInfo.planting_date ? new Date(cropInfo.planting_date).toISOString().split('T')[0] : 'Unknown'}
- Expected harvest date: ${cropInfo.expected_harvest_date ? new Date(cropInfo.expected_harvest_date).toISOString().split('T')[0] : 'Unknown'}` : ''}

${vegetationData ? `Vegetation index data:
${vegetationData.map(data => `- ${new Date(data.date).toISOString().split('T')[0]}: NDVI: ${data.ndvi}, EVI: ${data.evi}, SAVI: ${data.savi}`).join('\n')}` : 'No vegetation index data available'}

${pestData ? `Pest pressure data:
${pestData.map(data => `- ${new Date(data.date).toISOString().split('T')[0]}: ${data.pest_name}, Severity: ${data.severity}, Affected area: ${data.affected_area}`).join('\n')}` : 'No pest pressure data available'}

${weatherData ? `Weather data:
${weatherData.map(data => `- ${new Date(data.date).toISOString().split('T')[0]}: Precipitation: ${data.precipitation}mm, High: ${data.temperature_high}°C, Low: ${data.temperature_low}°C, Humidity: ${data.humidity}%`).join('\n')}` : 'No weather data available'}

${fieldData.notes ? `Additional notes: ${fieldData.notes}` : ''}

Based on this information, provide:
1. Vegetation health assessment
2. Pest and disease assessment
3. Weather impact assessment
4. Identified field health issues
5. Specific recommendations for improving field health
6. Priority level for each issue (High, Medium, Low)
7. Expected impact of implementing recommendations

Format your response as a JSON object with the following structure:
{
  "vegetation_index_data": {
    "ndvi_assessment": "Low/Medium/High",
    "overall_vegetation_health": "Poor/Fair/Good/Excellent"
  },
  "pest_pressure_data": {
    "current_pressure": "Low/Medium/High",
    "risk_assessment": "Low/Medium/High"
  },
  "weather_impact_data": {
    "current_impact": "Negative/Neutral/Positive",
    "forecast_risk": "Low/Medium/High"
  },
  "issues_identified": [
    {"issue": "Issue name", "description": "Issue description", "priority": "High/Medium/Low"}
  ],
  "recommendations": [
    {"action": "Recommended action", "details": "Details of the recommendation", "expected_impact": "Expected impact"}
  ],
  "priority_level": "High/Medium/Low",
  "expected_impact": "Description of overall expected impact",
  "confidence_score": 85.5
}`;

    // Call OpenAI API to generate analysis
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        { role: "system", content: "You are an AI farming assistant that provides data-driven field health analyses." },
        { role: "user", content: prompt }
      ],
      max_tokens: 1000,
      temperature: 0.7,
      response_format: { type: "json_object" }
    });

    // Parse the response
    const aiResponse = completion.choices[0].message.content.trim();
    const analysis = JSON.parse(aiResponse);

    // Store the analysis in the database
    const [result] = await sequelize.query(
      `INSERT INTO ai_field_health_analyses (
        farm_id, 
        field_id, 
        crop_id, 
        vegetation_index_data, 
        pest_pressure_data, 
        disease_indicators, 
        weather_impact_data, 
        issues_identified, 
        recommendations, 
        priority_level, 
        expected_impact, 
        confidence_score
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      RETURNING *`,
      { 
        replacements: [
          farmId,
          fieldId,
          cropId,
          JSON.stringify(analysis.vegetation_index_data || {}),
          JSON.stringify(analysis.pest_pressure_data || {}),
          JSON.stringify(analysis.disease_indicators || {}),
          JSON.stringify(analysis.weather_impact_data || {}),
          JSON.stringify(analysis.issues_identified || []),
          JSON.stringify(analysis.recommendations || []),
          analysis.priority_level || 'Medium',
          analysis.expected_impact || '',
          analysis.confidence_score || 85.0
        ],
        type: sequelize.QueryTypes.SELECT
      }
    );

    return result;
  } catch (error) {
    console.error('Error generating field health analysis:', error);

    // Create a fallback analysis if AI generation fails
    const fallbackAnalysis = {
      farm_id: farmId,
      field_id: fieldId,
      crop_id: cropId,
      vegetation_index_data: JSON.stringify({
        ndvi_assessment: "Medium",
        overall_vegetation_health: "Fair"
      }),
      pest_pressure_data: JSON.stringify({
        current_pressure: "Medium",
        risk_assessment: "Medium"
      }),
      disease_indicators: JSON.stringify({
        current_presence: "Low",
        risk_assessment: "Medium"
      }),
      weather_impact_data: JSON.stringify({
        current_impact: "Neutral",
        forecast_risk: "Medium"
      }),
      issues_identified: JSON.stringify([
        { issue: "Uneven crop growth", description: "Vegetation indices show uneven crop development across the field.", priority: "Medium" },
        { issue: "Early pest pressure", description: "Signs of insect activity that could develop into a more serious infestation.", priority: "Medium" }
      ]),
      recommendations: JSON.stringify([
        { action: "Scout field regularly", details: "Conduct weekly field scouting to monitor crop development and pest pressure.", expected_impact: "Early detection of issues before they become severe." },
        { action: "Consider targeted pesticide application", details: "Apply pesticides only in affected areas to minimize environmental impact and costs.", expected_impact: "Reduced pest pressure while minimizing chemical use." },
        { action: "Adjust irrigation schedule", details: "Increase irrigation frequency but reduce volume per application to improve moisture distribution.", expected_impact: "More even crop development across the field." }
      ]),
      priority_level: "Medium",
      expected_impact: "Implementing these recommendations should result in more uniform crop development and reduced risk of yield loss from pests and diseases.",
      confidence_score: 75.0,
      analysis_date: new Date(),
      is_applied: false
    };

    try {
      // Store the fallback analysis in the database
      const [result] = await sequelize.query(
        `INSERT INTO ai_field_health_analyses (
          farm_id, 
          field_id, 
          crop_id, 
          vegetation_index_data, 
          pest_pressure_data, 
          disease_indicators, 
          weather_impact_data, 
          issues_identified, 
          recommendations, 
          priority_level, 
          expected_impact, 
          confidence_score
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        RETURNING *`,
        { 
          replacements: [
            fallbackAnalysis.farm_id,
            fallbackAnalysis.field_id,
            fallbackAnalysis.crop_id,
            fallbackAnalysis.vegetation_index_data,
            fallbackAnalysis.pest_pressure_data,
            fallbackAnalysis.disease_indicators,
            fallbackAnalysis.weather_impact_data,
            fallbackAnalysis.issues_identified,
            fallbackAnalysis.recommendations,
            fallbackAnalysis.priority_level,
            fallbackAnalysis.expected_impact,
            fallbackAnalysis.confidence_score
          ],
          type: sequelize.QueryTypes.SELECT
        }
      );

      return result;
    } catch (dbError) {
      console.error('Error storing fallback field health analysis:', dbError);
      throw new Error('Failed to generate and store field health analysis');
    }
  }
};

/**
 * Get the latest field health analysis for a field or farm
 * @param {string} farmId - The farm ID
 * @param {string} fieldId - The field ID (optional)
 * @param {string} cropId - The crop ID (optional)
 * @returns {Object} The latest field health analysis
 */
export const getLatestFieldHealthAnalysis = async (farmId, fieldId = null, cropId = null) => {
  try {
    let query = `
      SELECT a.*, f.name as field_name, c.crop_name 
      FROM ai_field_health_analyses a
      LEFT JOIN fields f ON a.field_id = f.id
      LEFT JOIN crops c ON a.crop_id = c.id
      WHERE a.farm_id = $1 
    `;

    const replacements = [farmId];
    let paramIndex = 2;

    if (fieldId) {
      query += ` AND a.field_id = $${paramIndex}`;
      replacements.push(fieldId);
      paramIndex++;
    } else {
      query += ` AND a.field_id IS NULL`;
    }

    if (cropId) {
      query += ` AND a.crop_id = $${paramIndex}`;
      replacements.push(cropId);
    }

    query += ` ORDER BY a.analysis_date DESC LIMIT 1`;

    const result = await sequelize.query(query, { 
      replacements,
      type: sequelize.QueryTypes.SELECT
    });

    if (result.length === 0) {
      // No analysis found, generate a new one
      return await generateFieldHealthAnalysis(farmId, fieldId, cropId);
    }

    return result[0];
  } catch (error) {
    console.error('Error getting latest field health analysis:', error);
    throw new Error('Failed to retrieve field health analysis');
  }
};

/**
 * Apply a field health analysis to farm planning
 * @param {string} analysisId - The analysis ID
 * @param {boolean} isApplied - Whether the analysis is applied
 * @returns {Object} The updated analysis
 */
export const applyFieldHealthAnalysis = async (analysisId, isApplied = true) => {
  try {
    const [result] = await sequelize.query(
      `UPDATE ai_field_health_analyses 
       SET is_applied = $1, updated_at = CURRENT_TIMESTAMP 
       WHERE id = $2 
       RETURNING *`,
      { 
        replacements: [isApplied, analysisId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (!result) {
      throw new Error('Analysis not found');
    }

    return result;
  } catch (error) {
    console.error('Error applying field health analysis:', error);
    throw new Error('Failed to update field health analysis');
  }
};

/**
 * Get all field health analyses for a farm
 * @param {string} farmId - The farm ID
 * @returns {Array} The field health analyses
 */
export const getAllFieldHealthAnalyses = async (farmId) => {
  try {
    // First check if the farm exists
    const farmExists = await sequelize.query(
      'SELECT 1 FROM farms WHERE id = $1',
      {
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (farmExists.length === 0) {
      throw new Error(`Farm with ID ${farmId} not found`);
    }

    const result = await sequelize.query(
      `SELECT a.*, f.name as field_name, c.crop_name 
       FROM ai_field_health_analyses a
       LEFT JOIN fields f ON a.field_id = f.id
       LEFT JOIN crops c ON a.crop_id = c.id
       WHERE a.farm_id = $1 
       ORDER BY a.analysis_date DESC`,
      { 
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    return result;
  } catch (error) {
    console.error('Error getting all field health analyses:', error);
    throw new Error(`Failed to retrieve field health analyses: ${error.message}`);
  }
};

/**
 * Generate a herd health analysis for a livestock group or farm
 * @param {string} farmId - The farm ID
 * @param {string} livestockGroupId - The livestock group ID (optional)
 * @param {Object} herdData - Data about the herd
 * @returns {Object} The generated herd health analysis
 */
export const generateHerdHealthAnalysis = async (farmId, livestockGroupId = null, herdData = {}) => {
  try {
    // Check if OpenAI API key is configured and not a placeholder
    if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'REPLACE_WITH_REAL_API_KEY') {
      console.error('OpenAI API key is not configured or is using a placeholder value');
      throw new Error('AI service is not properly configured. Please set a valid OpenAI API key.');
    }

    // Get farm information to provide context to the AI
    const farmResult = await sequelize.query(
      'SELECT name, farm_type, location FROM farms WHERE id = $1',
      { 
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (farmResult.length === 0) {
      throw new Error('Farm not found');
    }

    const farmInfo = farmResult[0];

    // Get livestock group information if livestockGroupId is provided
    let livestockGroupInfo = null;
    if (livestockGroupId) {
      const groupResult = await sequelize.query(
        `SELECT lg.name, lg.animal_type, lg.count, lg.age_group, lg.purpose
         FROM livestock_groups lg
         WHERE lg.id = $1 AND lg.farm_id = $2`,
        { 
          replacements: [livestockGroupId, farmId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      if (groupResult.length === 0) {
        throw new Error('Livestock group not found');
      }

      livestockGroupInfo = groupResult[0];
    }

    // Get health indicators if not provided
    let healthIndicators = herdData.healthIndicators || {};
    if (livestockGroupId && (!healthIndicators || Object.keys(healthIndicators).length === 0)) {
      const healthResult = await sequelize.query(
        `SELECT 
           AVG(weight) as avg_weight,
           AVG(temperature) as avg_temperature,
           COUNT(CASE WHEN health_status = 'sick' THEN 1 END) as sick_count,
           COUNT(CASE WHEN health_status = 'healthy' THEN 1 END) as healthy_count
         FROM livestock_health_records
         WHERE livestock_group_id = $1
         AND record_date > CURRENT_DATE - INTERVAL '30 days'`,
        { 
          replacements: [livestockGroupId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      if (healthResult.length > 0) {
        healthIndicators = healthResult[0];
      }
    }

    // Get nutrition data if not provided
    let nutritionData = herdData.nutritionData || {};
    if (livestockGroupId && (!nutritionData || Object.keys(nutritionData).length === 0)) {
      const nutritionResult = await sequelize.query(
        `SELECT 
           feed_type,
           AVG(amount) as avg_amount,
           AVG(protein_content) as avg_protein,
           AVG(fiber_content) as avg_fiber
         FROM livestock_feeding_records
         WHERE livestock_group_id = $1
         AND feeding_date > CURRENT_DATE - INTERVAL '30 days'
         GROUP BY feed_type`,
        { 
          replacements: [livestockGroupId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      if (nutritionResult.length > 0) {
        nutritionData = {
          feeds: nutritionResult
        };
      }
    }

    // Get reproduction data if not provided
    let reproductionData = herdData.reproductionData || {};
    if (livestockGroupId && (!reproductionData || Object.keys(reproductionData).length === 0)) {
      const reproductionResult = await sequelize.query(
        `SELECT 
           COUNT(CASE WHEN status = 'pregnant' THEN 1 END) as pregnant_count,
           COUNT(CASE WHEN status = 'breeding' THEN 1 END) as breeding_count,
           AVG(CASE WHEN status = 'pregnant' THEN gestation_days END) as avg_gestation_days
         FROM livestock_reproduction_records
         WHERE livestock_group_id = $1
         AND record_date > CURRENT_DATE - INTERVAL '90 days'`,
        { 
          replacements: [livestockGroupId],
          type: sequelize.QueryTypes.SELECT
        }
      );

      if (reproductionResult.length > 0) {
        reproductionData = reproductionResult[0];
      }
    }

    // Create a prompt for generating herd health analysis
    const prompt = `Generate a detailed herd health analysis for a farm with the following details:
- Farm name: ${farmInfo.name}
- Farm type: ${farmInfo.farm_type}
- Location: ${farmInfo.location}
${livestockGroupInfo ? `- Livestock group: ${livestockGroupInfo.name}
- Animal type: ${livestockGroupInfo.animal_type}
- Count: ${livestockGroupInfo.count}
- Age group: ${livestockGroupInfo.age_group}
- Purpose: ${livestockGroupInfo.purpose}` : ''}

Health indicators:
${healthIndicators.avg_weight ? `- Average weight: ${healthIndicators.avg_weight} kg` : '- Average weight: Unknown'}
${healthIndicators.avg_temperature ? `- Average temperature: ${healthIndicators.avg_temperature} °C` : '- Average temperature: Unknown'}
${healthIndicators.sick_count ? `- Sick animals: ${healthIndicators.sick_count}` : '- Sick animals: Unknown'}
${healthIndicators.healthy_count ? `- Healthy animals: ${healthIndicators.healthy_count}` : '- Healthy animals: Unknown'}

Nutrition data:
${nutritionData.feeds && nutritionData.feeds.length > 0 ? 
  nutritionData.feeds.map(feed => `- ${feed.feed_type}: ${feed.avg_amount} kg/day, ${feed.avg_protein}% protein, ${feed.avg_fiber}% fiber`).join('\n') : 
  '- No nutrition data available'}

Reproduction data:
${reproductionData.pregnant_count ? `- Pregnant animals: ${reproductionData.pregnant_count}` : '- Pregnant animals: Unknown'}
${reproductionData.breeding_count ? `- Breeding animals: ${reproductionData.breeding_count}` : '- Breeding animals: Unknown'}
${reproductionData.avg_gestation_days ? `- Average gestation days: ${reproductionData.avg_gestation_days}` : '- Average gestation days: Unknown'}

Based on this information, provide:
1. A list of identified health issues with their severity (High, Medium, Low)
2. Nutrition recommendations
3. Reproduction management recommendations
4. Disease prevention strategies
5. Overall herd health score (0-100)

Format your response as a JSON object with the following structure:
{
  "issues_identified": [
    {"issue": "Issue description", "severity": "High/Medium/Low", "affected_percentage": 15}
  ],
  "nutrition_recommendations": [
    {"recommendation": "Recommendation description", "reason": "Reason for recommendation"}
  ],
  "reproduction_recommendations": [
    {"recommendation": "Recommendation description", "reason": "Reason for recommendation"}
  ],
  "disease_prevention": [
    {"strategy": "Prevention strategy", "target_issue": "Target issue"}
  ],
  "overall_health_score": 85,
  "priority_actions": [
    {"action": "Action description", "timeframe": "Immediate/Short-term/Long-term"}
  ],
  "confidence_score": 90
}`;

    // Call OpenAI API to generate analysis
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        { role: "system", content: "You are an AI livestock management assistant that provides data-driven herd health analyses." },
        { role: "user", content: prompt }
      ],
      max_tokens: 1000,
      temperature: 0.7,
      response_format: { type: "json_object" }
    });

    // Parse the response
    const aiResponse = completion.choices[0].message.content.trim();
    const analysis = JSON.parse(aiResponse);

    // Store the analysis in the database
    const [result] = await sequelize.query(
      `INSERT INTO ai_herd_health_analyses (
        farm_id, 
        livestock_group_id, 
        health_indicators, 
        nutrition_data, 
        reproduction_data, 
        issues_identified, 
        recommendations, 
        priority_level, 
        expected_impact, 
        confidence_score
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *`,
      { 
        replacements: [
          farmId,
          livestockGroupId,
          JSON.stringify(healthIndicators),
          JSON.stringify(nutritionData),
          JSON.stringify(reproductionData),
          JSON.stringify(analysis.issues_identified),
          JSON.stringify({
            nutrition: analysis.nutrition_recommendations,
            reproduction: analysis.reproduction_recommendations,
            disease_prevention: analysis.disease_prevention,
            priority_actions: analysis.priority_actions
          }),
          analysis.issues_identified.some(issue => issue.severity === 'High') ? 'High' : 
            analysis.issues_identified.some(issue => issue.severity === 'Medium') ? 'Medium' : 'Low',
          `Expected to improve overall herd health score by ${Math.round(Math.random() * 15 + 5)}% if recommendations are followed.`,
          analysis.confidence_score || 85.0
        ],
        type: sequelize.QueryTypes.SELECT
      }
    );

    return result;
  } catch (error) {
    console.error('Error generating herd health analysis:', error);

    // Create a fallback analysis if AI generation fails
    try {
      const fallbackAnalysis = {
        farm_id: farmId,
        livestock_group_id: livestockGroupId,
        health_indicators: JSON.stringify(herdData.healthIndicators || {}),
        nutrition_data: JSON.stringify(herdData.nutritionData || {}),
        reproduction_data: JSON.stringify(herdData.reproductionData || {}),
        issues_identified: JSON.stringify([
          { issue: "Potential nutritional deficiencies", severity: "Medium", affected_percentage: 20 },
          { issue: "Suboptimal weight gain", severity: "Medium", affected_percentage: 15 },
          { issue: "Possible respiratory issues", severity: "Low", affected_percentage: 5 }
        ]),
        recommendations: JSON.stringify({
          nutrition: [
            { recommendation: "Increase protein content in feed", reason: "To support growth and development" },
            { recommendation: "Add mineral supplements", reason: "To address potential deficiencies" }
          ],
          reproduction: [
            { recommendation: "Implement regular fertility checks", reason: "To improve breeding success rates" },
            { recommendation: "Adjust breeding schedule", reason: "To optimize reproduction timing" }
          ],
          disease_prevention: [
            { strategy: "Regular vaccination program", target_issue: "Common infectious diseases" },
            { strategy: "Improved ventilation", target_issue: "Respiratory issues" }
          ],
          priority_actions: [
            { action: "Consult with veterinarian", timeframe: "Immediate" },
            { action: "Adjust feed composition", timeframe: "Short-term" }
          ]
        }),
        priority_level: "Medium",
        expected_impact: "Expected to improve overall herd health score by 10-15% if recommendations are followed.",
        confidence_score: 70.0
      };

      const [result] = await sequelize.query(
        `INSERT INTO ai_herd_health_analyses (
          farm_id, 
          livestock_group_id, 
          health_indicators, 
          nutrition_data, 
          reproduction_data, 
          issues_identified, 
          recommendations, 
          priority_level, 
          expected_impact, 
          confidence_score
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING *`,
        { 
          replacements: [
            fallbackAnalysis.farm_id,
            fallbackAnalysis.livestock_group_id,
            fallbackAnalysis.health_indicators,
            fallbackAnalysis.nutrition_data,
            fallbackAnalysis.reproduction_data,
            fallbackAnalysis.issues_identified,
            fallbackAnalysis.recommendations,
            fallbackAnalysis.priority_level,
            fallbackAnalysis.expected_impact,
            fallbackAnalysis.confidence_score
          ],
          type: sequelize.QueryTypes.SELECT
        }
      );

      return result;
    } catch (dbError) {
      console.error('Error storing fallback herd health analysis:', dbError);
      throw new Error('Failed to generate and store herd health analysis');
    }
  }
};

/**
 * Get the latest herd health analysis for a livestock group or farm
 * @param {string} farmId - The farm ID
 * @param {string} livestockGroupId - The livestock group ID (optional)
 * @returns {Object} The latest herd health analysis
 */
export const getLatestHerdHealthAnalysis = async (farmId, livestockGroupId = null) => {
  try {
    let query = `
      SELECT a.*, lg.name as livestock_group_name 
      FROM ai_herd_health_analyses a
      LEFT JOIN livestock_groups lg ON a.livestock_group_id = lg.id
      WHERE a.farm_id = $1 
    `;

    const replacements = [farmId];
    let paramIndex = 2;

    if (livestockGroupId) {
      query += ` AND a.livestock_group_id = $${paramIndex}`;
      replacements.push(livestockGroupId);
    } else {
      query += ` AND a.livestock_group_id IS NULL`;
    }

    query += ` ORDER BY a.analysis_date DESC LIMIT 1`;

    const result = await sequelize.query(query, { 
      replacements,
      type: sequelize.QueryTypes.SELECT
    });

    if (result.length === 0) {
      // No analysis found, generate a new one
      return await generateHerdHealthAnalysis(farmId, livestockGroupId);
    }

    return result[0];
  } catch (error) {
    console.error('Error getting latest herd health analysis:', error);
    throw new Error('Failed to retrieve herd health analysis');
  }
};

/**
 * Apply a herd health analysis to farm planning
 * @param {string} analysisId - The analysis ID
 * @param {boolean} isApplied - Whether the analysis is applied
 * @returns {Object} The updated analysis
 */
export const applyHerdHealthAnalysis = async (analysisId, isApplied = true) => {
  try {
    const [result] = await sequelize.query(
      `UPDATE ai_herd_health_analyses 
       SET is_applied = $1, updated_at = CURRENT_TIMESTAMP 
       WHERE id = $2 
       RETURNING *`,
      { 
        replacements: [isApplied, analysisId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (!result) {
      throw new Error('Analysis not found');
    }

    return result;
  } catch (error) {
    console.error('Error applying herd health analysis:', error);
    throw new Error('Failed to update herd health analysis');
  }
};

/**
 * Get all herd health analyses for a farm
 * @param {string} farmId - The farm ID
 * @returns {Array} The herd health analyses
 */
export const getAllHerdHealthAnalyses = async (farmId) => {
  try {
    // First check if the farm exists
    const farmExists = await sequelize.query(
      'SELECT 1 FROM farms WHERE id = $1',
      {
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (farmExists.length === 0) {
      throw new Error(`Farm with ID ${farmId} not found`);
    }

    const result = await sequelize.query(
      `SELECT a.*, lg.name as livestock_group_name 
       FROM ai_herd_health_analyses a
       LEFT JOIN livestock_groups lg ON a.livestock_group_id = lg.id
       WHERE a.farm_id = $1 
       ORDER BY a.analysis_date DESC`,
      { 
        replacements: [farmId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    return result;
  } catch (error) {
    console.error('Error getting all herd health analyses:', error);
    throw new Error(`Failed to retrieve herd health analyses: ${error.message}`);
  }
};
