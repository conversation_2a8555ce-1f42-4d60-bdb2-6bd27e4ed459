import { sequelize } from '../config/database.js';
import { Op } from 'sequelize';
import crypto from 'crypto';

/**
 * Service for managing external API data
 * This service provides functions for storing API requests, retrieving cached responses,
 * and updating analytics data.
 */

// Import models
import <PERSON><PERSON><PERSON><PERSON>ider from '../models/ApiProvider.js';
import ApiEndpoint from '../models/ApiEndpoint.js';
import ApiRequest from '../models/ApiRequest.js';
import ApiCache from '../models/ApiCache.js';
import ApiAnalytics from '../models/ApiAnalytics.js';

/**
 * Generate a cache key for an API request
 * @param {string} endpointId - The ID of the API endpoint
 * @param {Object} params - The request parameters
 * @returns {string} - A unique cache key
 */
const generateCacheKey = (endpointId, params) => {
  const paramsString = JSON.stringify(params);
  return crypto.createHash('md5').update(`${endpointId}:${paramsString}`).digest('hex');
};

/**
 * Get a cached API response if available
 * @param {string} endpointId - The ID of the API endpoint
 * @param {Object} params - The request parameters
 * @returns {Object|null} - The cached response or null if not found
 */
export const getCachedResponse = async (endpointId, params) => {
  try {
    const cacheKey = generateCacheKey(endpointId, params);

    const cachedResponse = await ApiCache.findOne({
      where: {
        endpoint_id: endpointId,
        cache_key: cacheKey,
        expires_at: {
          [Op.gt]: new Date()
        }
      }
    });

    if (cachedResponse) {
      // Update last accessed time
      await cachedResponse.update({
        last_accessed_at: new Date()
      });

      return cachedResponse.response_body;
    }

    return null;
  } catch (error) {
    console.error('Error getting cached response:', error);
    return null;
  }
};

/**
 * Store an API response in the cache
 * @param {string} endpointId - The ID of the API endpoint
 * @param {Object} params - The request parameters
 * @param {Object} responseBody - The response body to cache
 * @param {number} cacheDuration - Cache duration in seconds (optional)
 * @returns {boolean} - Whether the response was successfully cached
 */
export const cacheApiResponse = async (endpointId, params, responseBody, cacheDuration) => {
  try {
    const cacheKey = generateCacheKey(endpointId, params);

    // Get the endpoint to determine cache duration if not provided
    if (!cacheDuration) {
      const endpoint = await ApiEndpoint.findByPk(endpointId);
      if (endpoint) {
        cacheDuration = endpoint.cache_duration_seconds;
      } else {
        cacheDuration = 3600; // Default to 1 hour
      }
    }

    // Calculate expiration time
    const expiresAt = new Date();
    expiresAt.setSeconds(expiresAt.getSeconds() + cacheDuration);

    // Check if cache entry already exists
    const existingCache = await ApiCache.findOne({
      where: {
        endpoint_id: endpointId,
        cache_key: cacheKey
      }
    });

    if (existingCache) {
      // Update existing cache entry
      await existingCache.update({
        response_body: responseBody,
        expires_at: expiresAt,
        last_accessed_at: new Date()
      });
    } else {
      // Create new cache entry
      await ApiCache.create({
        endpoint_id: endpointId,
        cache_key: cacheKey,
        request_params: params,
        response_body: responseBody,
        expires_at: expiresAt
      });
    }

    return true;
  } catch (error) {
    console.error('Error caching API response:', error);
    return false;
  }
};

/**
 * Record an API request and its response
 * @param {string} endpointId - The ID of the API endpoint
 * @param {Object} requestData - Data about the request
 * @param {Object} responseData - Data about the response
 * @returns {Object} - The created API request record
 */
export const recordApiRequest = async (endpointId, requestData, responseData) => {
  try {
    const {
      farmId,
      fieldId,
      userId,
      params,
      headers,
      body
    } = requestData;

    const {
      status,
      headers: responseHeaders,
      body: responseBody,
      error,
      duration,
      cacheHit,
      expiresAt
    } = responseData;

    const requestTime = new Date();
    const responseTime = new Date();
    responseTime.setMilliseconds(responseTime.getMilliseconds() + duration);

    const apiRequest = await ApiRequest.create({
      endpoint_id: endpointId,
      farm_id: farmId,
      field_id: fieldId,
      user_id: userId,
      request_params: params || {},
      request_headers: headers,
      request_body: body,
      response_status: status,
      response_headers: responseHeaders,
      response_body: responseBody,
      error_message: error,
      request_time: requestTime,
      response_time: responseTime,
      duration_ms: duration,
      cache_hit: cacheHit,
      expires_at: expiresAt
    });

    // Update analytics
    await updateApiAnalytics(endpointId, status, duration, cacheHit);

    return apiRequest;
  } catch (error) {
    console.error('Error recording API request:', error);
    return null;
  }
};

/**
 * Update API analytics data
 * @param {string} endpointId - The ID of the API endpoint
 * @param {number} status - The HTTP status code of the response
 * @param {number} duration - The duration of the request in milliseconds
 * @param {boolean} cacheHit - Whether the response was served from cache
 * @returns {boolean} - Whether the analytics were successfully updated
 */
export const updateApiAnalytics = async (endpointId, status, duration, cacheHit) => {
  try {
    // Get the endpoint to determine the provider
    const endpoint = await ApiEndpoint.findByPk(endpointId);
    if (!endpoint) {
      console.error(`Endpoint with ID ${endpointId} not found`);
      return false;
    }

    const providerId = endpoint.provider_id;
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD

    // Update provider-level analytics
    let providerAnalytics = await ApiAnalytics.findOne({
      where: {
        provider_id: providerId,
        endpoint_id: null,
        date: today
      }
    });

    if (providerAnalytics) {
      // Update existing analytics
      await providerAnalytics.update({
        request_count: sequelize.literal('request_count + 1'),
        error_count: sequelize.literal(`error_count + ${status >= 400 ? 1 : 0}`),
        cache_hit_count: sequelize.literal(`cache_hit_count + ${cacheHit ? 1 : 0}`),
        avg_response_time_ms: sequelize.literal(`(avg_response_time_ms * request_count + ${duration}) / (request_count + 1)`)
      });
    } else {
      // Create new analytics
      await ApiAnalytics.create({
        provider_id: providerId,
        endpoint_id: null,
        date: today,
        request_count: 1,
        error_count: status >= 400 ? 1 : 0,
        cache_hit_count: cacheHit ? 1 : 0,
        avg_response_time_ms: duration
      });
    }

    // Update endpoint-level analytics
    let endpointAnalytics = await ApiAnalytics.findOne({
      where: {
        provider_id: providerId,
        endpoint_id: endpointId,
        date: today
      }
    });

    if (endpointAnalytics) {
      // Update existing analytics
      await endpointAnalytics.update({
        request_count: sequelize.literal('request_count + 1'),
        error_count: sequelize.literal(`error_count + ${status >= 400 ? 1 : 0}`),
        cache_hit_count: sequelize.literal(`cache_hit_count + ${cacheHit ? 1 : 0}`),
        avg_response_time_ms: sequelize.literal(`(avg_response_time_ms * request_count + ${duration}) / (request_count + 1)`)
      });
    } else {
      // Create new analytics
      await ApiAnalytics.create({
        provider_id: providerId,
        endpoint_id: endpointId,
        date: today,
        request_count: 1,
        error_count: status >= 400 ? 1 : 0,
        cache_hit_count: cacheHit ? 1 : 0,
        avg_response_time_ms: duration
      });
    }

    return true;
  } catch (error) {
    console.error('Error updating API analytics:', error);
    return false;
  }
};

/**
 * Clean up expired cache entries
 * @param {number} olderThanDays - Remove entries older than this many days (default: 7)
 * @returns {number} - Number of entries removed
 */
export const cleanupExpiredCache = async (olderThanDays = 7) => {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const result = await ApiCache.destroy({
      where: {
        expires_at: {
          [Op.lt]: new Date()
        },
        last_accessed_at: {
          [Op.lt]: cutoffDate
        }
      }
    });

    console.log(`Removed ${result} expired cache entries`);
    return result;
  } catch (error) {
    console.error('Error cleaning up expired cache:', error);
    return 0;
  }
};

/**
 * Get API provider by name
 * @param {string} name - The name of the API provider
 * @returns {Object|null} - The API provider or null if not found
 */
export const getApiProviderByName = async (name) => {
  try {
    return await ApiProvider.findOne({
      where: {
        name
      }
    });
  } catch (error) {
    console.error('Error getting API provider:', error);
    return null;
  }
};

/**
 * Get API endpoint by provider and name
 * @param {string} providerId - The ID of the API provider
 * @param {string} name - The name of the API endpoint
 * @returns {Object|null} - The API endpoint or null if not found
 */
export const getApiEndpointByName = async (providerId, name) => {
  try {
    return await ApiEndpoint.findOne({
      where: {
        provider_id: providerId,
        name
      }
    });
  } catch (error) {
    console.error('Error getting API endpoint:', error);
    return null;
  }
};

/**
 * Make an API request with caching
 * @param {string} endpointId - The ID of the API endpoint
 * @param {Object} requestData - Data about the request
 * @param {Function} requestFn - Function to make the actual API request
 * @returns {Object} - The API response
 */
export const makeApiRequestWithCaching = async (endpointId, requestData, requestFn) => {
  const startTime = Date.now();
  let cacheHit = false;
  let response = null;
  let error = null;

  try {
    // Check cache first
    const cachedResponse = await getCachedResponse(endpointId, requestData.params);

    if (cachedResponse) {
      // Cache hit
      cacheHit = true;
      response = cachedResponse;
    } else {
      // Cache miss, make the actual request
      response = await requestFn();

      // Cache the response
      if (response && response.status >= 200 && response.status < 300) {
        await cacheApiResponse(endpointId, requestData.params, response.data);
      }
    }

    // Record the request
    const duration = Date.now() - startTime;
    await recordApiRequest(endpointId, requestData, {
      status: response?.status || 500,
      headers: response?.headers,
      body: response?.data,
      error: error?.message,
      duration,
      cacheHit,
      expiresAt: null // This would be set by cacheApiResponse
    });

    return response;
  } catch (err) {
    error = err;

    // Record the failed request
    const duration = Date.now() - startTime;
    await recordApiRequest(endpointId, requestData, {
      status: err.response?.status || 500,
      headers: err.response?.headers,
      body: err.response?.data,
      error: err.message,
      duration,
      cacheHit,
      expiresAt: null
    });

    throw err;
  }
};
