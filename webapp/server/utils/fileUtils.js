import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { promisify } from 'util';
import { fileTypeFromBuffer } from 'file-type';
import pdfParse from 'pdf-parse';
import Document from '../models/Document.js';
import FarmStorageUsage from '../models/FarmStorageUsage.js';
import SubscriptionPlan from '../models/SubscriptionPlan.js';
import Tenant from '../models/Tenant.js';
import Farm from '../models/Farm.js';
import { uploadToSpaces, downloadFromSpaces, deleteFromSpaces, fileExistsInSpaces } from './spacesUtils.js';
import { encryptFile, decryptFile } from './encryptionUtils.js';

// Get directory name for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create test data directory if it doesn't exist
const ensureTestDataDirectory = () => {
  const testDataDir = path.join(__dirname, '..', '..', 'test', 'data');
  if (!fs.existsSync(testDataDir)) {
    fs.mkdirSync(testDataDir, { recursive: true });
  }
  return testDataDir;
};

// Convert fs functions to promise-based
const mkdir = promisify(fs.mkdir);
const writeFile = promisify(fs.writeFile);
const readFile = promisify(fs.readFile);
const unlink = promisify(fs.unlink);

// Base upload directory
const uploadsDir = path.join(__dirname, '..', '..', 'uploads');

// Allowed file types and their MIME types
const ALLOWED_FILE_TYPES = {
  // Documents
  'pdf': 'application/pdf',
  'doc': 'application/msword',
  'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'txt': 'text/plain',
  'rtf': 'application/rtf',
  'odt': 'application/vnd.oasis.opendocument.text',

  // Spreadsheets
  'xls': 'application/vnd.ms-excel',
  'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'csv': 'text/csv',
  'ods': 'application/vnd.oasis.opendocument.spreadsheet',

  // Presentations
  'ppt': 'application/vnd.ms-powerpoint',
  'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'odp': 'application/vnd.oasis.opendocument.presentation',

  // Images
  'jpg': 'image/jpeg',
  'jpeg': 'image/jpeg',
  'png': 'image/png',
  'gif': 'image/gif',
  'bmp': 'image/bmp',
  'tiff': 'image/tiff',
  'tif': 'image/tiff',
  'svg': 'image/svg+xml',

  // Audio
  'mp3': 'audio/mpeg',
  'wav': 'audio/wav',
  'ogg': 'audio/ogg',

  // Video
  'mp4': 'video/mp4',
  'avi': 'video/x-msvideo',
  'mov': 'video/quicktime',
  'wmv': 'video/x-ms-wmv',

  // Archives
  'zip': 'application/zip',
  'rar': 'application/x-rar-compressed',
  'tar': 'application/x-tar',
  'gz': 'application/gzip',

  // Other
  'json': 'application/json',
  'xml': 'application/xml'
};

// Dangerous file extensions that should be blocked
const DANGEROUS_EXTENSIONS = [
  'exe', 'dll', 'bat', 'cmd', 'sh', 'js', 'vbs', 'ps1', 'msi', 'com', 'jar', 'jnlp',
  'app', 'dmg', 'pkg', 'deb', 'rpm', 'apk', 'scr', 'sys', 'php', 'asp', 'aspx', 'jsp',
  'cgi', 'pl', 'py', 'rb'
];

/**
 * Validates if a file is allowed based on its extension and MIME type
 * @param {string} filename - The name of the file
 * @param {Buffer} fileBuffer - The file buffer for MIME type detection
 * @returns {Promise<{valid: boolean, reason: string|null, detectedType: string|null}>}
 */
export const validateFileType = async (filename, fileBuffer) => {
  try {
    // Get file extension
    const ext = path.extname(filename).toLowerCase().substring(1);

    // Check if extension is in dangerous list
    if (DANGEROUS_EXTENSIONS.includes(ext)) {
      return {
        valid: false,
        reason: 'File type is not allowed for security reasons',
        detectedType: null
      };
    }

    // Check if extension is in allowed list
    if (!Object.keys(ALLOWED_FILE_TYPES).includes(ext)) {
      return {
        valid: false,
        reason: 'File type is not supported',
        detectedType: null
      };
    }

    // Detect actual file type from buffer
    const fileType = await fileTypeFromBuffer(fileBuffer);

    // If file type couldn't be detected, check if it's a text file
    if (!fileType && ext === 'txt') {
      // Simple check for text files
      const isText = isTextFile(fileBuffer);
      if (isText) {
        return {
          valid: true,
          reason: null,
          detectedType: 'text/plain'
        };
      }
    }

    // If file type couldn't be detected for other formats, reject
    if (!fileType && ext !== 'txt') {
      return {
        valid: false,
        reason: 'Could not determine file type',
        detectedType: null
      };
    }

    // If detected MIME type doesn't match expected MIME type for the extension
    if (fileType && fileType.mime !== ALLOWED_FILE_TYPES[ext]) {
      return {
        valid: false,
        reason: 'File extension does not match actual file type',
        detectedType: fileType.mime
      };
    }

    return {
      valid: true,
      reason: null,
      detectedType: fileType ? fileType.mime : ALLOWED_FILE_TYPES[ext]
    };
  } catch (error) {
    console.error('Error validating file type:', error);
    return {
      valid: false,
      reason: 'Error validating file type',
      detectedType: null
    };
  }
};

/**
 * Simple check if a buffer contains text
 * @param {Buffer} buffer - The file buffer
 * @returns {boolean}
 */
const isTextFile = (buffer) => {
  // Check a sample of the file for non-text characters
  const sample = buffer.slice(0, Math.min(buffer.length, 1000));
  for (let i = 0; i < sample.length; i++) {
    const byte = sample[i];
    // Control characters that aren't whitespace
    if (byte < 32 && ![9, 10, 13].includes(byte)) {
      return false;
    }
  }
  return true;
};

/**
 * Generates a storage path for a file
 * @param {string} farmId - The farm ID
 * @param {string} userId - The user ID who uploaded the file
 * @param {string} filename - The original filename
 * @returns {string} The storage path relative to the uploads directory
 */
export const generateStoragePath = (farmId, userId, filename) => {
  // Ensure farmId is valid, use a default if not
  const safeFarmId = farmId || 'default';

  const timestamp = Date.now();
  const sanitizedFilename = path.basename(filename).replace(/[^a-zA-Z0-9_.-]/g, '_');

  // Create the directory structure
  const storagePath = path.join(
    safeFarmId,
    'documents',
    new Date().getFullYear().toString(),
    (new Date().getMonth() + 1).toString().padStart(2, '0'),
    `${timestamp}_${userId.substring(0, 8)}_${sanitizedFilename}`
  );

  return storagePath;
};

/**
 * Saves a file to Digital Ocean Spaces (or filesystem as fallback)
 * @param {Buffer|string} fileData - The file data or path to temp file
 * @param {string} storagePath - The storage path relative to the uploads directory
 * @returns {Promise<string>} The storage path where the file was saved
 */
export const saveFile = async (fileData, storagePath) => {
  try {
    // Use Digital Ocean Spaces for storage
    await uploadToSpaces(fileData, storagePath);
    return storagePath;
  } catch (error) {
    console.error('Error saving to Spaces, falling back to local storage:', error);

    // Fallback to local storage
    const fullPath = path.join(uploadsDir, storagePath);
    const dirPath = path.dirname(fullPath);

    // Ensure the farm directory exists at the root level
    const pathParts = storagePath.split(path.sep);
    if (pathParts.length > 0) {
      const farmId = pathParts[0];
      const farmDir = path.join(uploadsDir, farmId);

      // Create farm directory if it doesn't exist
      if (farmId && !fs.existsSync(farmDir)) {
        await mkdir(farmDir, { recursive: true });
        console.log(`Created root directory for farm: ${farmId}`);
      }
    }

    // Create directory if it doesn't exist
    await mkdir(dirPath, { recursive: true });

    // If fileData is a string, it's a path to a temp file
    if (typeof fileData === 'string') {
      // Copy the temp file to the destination
      await fs.promises.copyFile(fileData, fullPath);
    } else {
      // Write the buffer to the file
      await writeFile(fullPath, fileData);
    }

    return fullPath;
  }
};

/**
 * Deletes a file from Digital Ocean Spaces (or filesystem as fallback)
 * @param {string} storagePath - The storage path relative to the uploads directory
 * @returns {Promise<boolean>} Whether the file was deleted successfully
 */
export const deleteFile = async (storagePath) => {
  try {
    // Try to delete from Spaces first
    const spacesResult = await deleteFromSpaces(storagePath);

    // If successful, return true
    if (spacesResult) {
      return true;
    }

    // If not successful, try local filesystem as fallback
    const fullPath = path.join(uploadsDir, storagePath);
    if (fs.existsSync(fullPath)) {
      await unlink(fullPath);
      return true;
    }

    return false;
  } catch (error) {
    console.error('Error deleting file:', error);
    return false;
  }
};

/**
 * Extracts text content from a file for search indexing
 * @param {string} filePath - The full path to the file or storage path in Spaces
 * @param {string} mimeType - The MIME type of the file
 * @returns {Promise<string|null>} The extracted text or null if extraction failed
 */
export const extractTextContent = async (filePath, mimeType) => {
  try {
    let dataBuffer;

    // First try to get the file from Spaces
    try {
      const fileExists = await fileExistsInSpaces(filePath);
      if (fileExists) {
        dataBuffer = await downloadFromSpaces(filePath);
      }
    } catch (error) {
      console.log('File not found in Spaces, trying local filesystem');
    }

    // If not found in Spaces, try local filesystem
    if (!dataBuffer) {
      const fullPath = path.join(uploadsDir, filePath);
      if (!fs.existsSync(fullPath)) {
        console.error(`File not found: ${fullPath}`);
        return null;
      }
      dataBuffer = await readFile(fullPath);
    }

    if (mimeType === 'application/pdf') {
      const pdfData = await pdfParse(dataBuffer);
      return pdfData.text;
    } else if (mimeType === 'text/plain') {
      return dataBuffer.toString('utf8');
    } else if (mimeType.startsWith('image/')) {
      // For images, we could use OCR in the future
      return null;
    } else {
      // For other file types, we don't extract text yet
      return null;
    }
  } catch (error) {
    console.error('Error extracting text content:', error);
    return null;
  }
};

/**
 * Reads a file's content as text
 * @param {string} filePath - The full path to the file
 * @returns {Promise<string>} The file content as text
 */
export const readFileContent = async (filePath) => {
  try {
    const data = await readFile(filePath);
    return data.toString('utf8');
  } catch (error) {
    console.error('Error reading file content:', error);
    throw error;
  }
};

/**
 * Checks if a farm has enough storage quota for a new file
 * @param {string} farmId - The farm ID
 * @param {number} fileSize - The size of the file in bytes
 * @returns {Promise<{allowed: boolean, reason: string|null, currentUsage: number, quota: number}>}
 */
export const checkStorageQuota = async (farmId, fileSize) => {
  try {
    // Get farm's subscription plan through tenant
    const farm = await Farm.findByPk(farmId, {
      include: [{ 
        model: Tenant,
        include: [{ model: SubscriptionPlan }]
      }]
    });

    if (!farm || !farm.Tenant || !farm.Tenant.SubscriptionPlan) {
      return {
        allowed: false,
        reason: 'Farm has no active subscription plan',
        currentUsage: 0,
        quota: 0
      };
    }

    // Get storage quota from subscription plan (convert GB to bytes)
    const storageQuotaBytes = farm.Tenant.SubscriptionPlan.storage_quota_gb * 1024 * 1024 * 1024;

    // Get current storage usage
    let storageUsage = await FarmStorageUsage.findOne({
      where: { farm_id: farmId }
    });

    if (!storageUsage) {
      // Create storage usage record if it doesn't exist
      storageUsage = await FarmStorageUsage.create({
        farm_id: farmId,
        total_bytes_used: 0,
        document_count: 0,
        external_document_count: 0,
        last_calculated_at: new Date()
      });
    }

    // Check if adding the new file would exceed the quota
    const newTotalUsage = storageUsage.total_bytes_used + fileSize;

    if (newTotalUsage > storageQuotaBytes) {
      return {
        allowed: false,
        reason: 'Storage quota exceeded',
        currentUsage: storageUsage.total_bytes_used,
        quota: storageQuotaBytes
      };
    }

    // Check if file size exceeds max file size
    const maxFileSizeBytes = farm.Tenant.SubscriptionPlan.max_file_size_mb * 1024 * 1024;

    if (fileSize > maxFileSizeBytes) {
      return {
        allowed: false,
        reason: 'File size exceeds maximum allowed size',
        currentUsage: storageUsage.total_bytes_used,
        quota: storageQuotaBytes
      };
    }

    return {
      allowed: true,
      reason: null,
      currentUsage: storageUsage.total_bytes_used,
      quota: storageQuotaBytes
    };
  } catch (error) {
    console.error('Error checking storage quota:', error);
    return {
      allowed: false,
      reason: 'Error checking storage quota',
      currentUsage: 0,
      quota: 0
    };
  }
};

/**
 * Updates the storage usage for a farm
 * @param {string} farmId - The farm ID
 * @param {number} bytesAdded - The number of bytes added (positive) or removed (negative)
 * @param {boolean} isExternal - Whether the document is external
 * @param {number} countChange - The change in document count (1 for add, -1 for remove)
 * @returns {Promise<boolean>} Whether the update was successful
 */
export const updateStorageUsage = async (farmId, bytesAdded, isExternal, countChange) => {
  try {
    let storageUsage = await FarmStorageUsage.findOne({
      where: { farm_id: farmId }
    });

    if (!storageUsage) {
      // Create storage usage record if it doesn't exist
      storageUsage = await FarmStorageUsage.create({
        farm_id: farmId,
        total_bytes_used: Math.max(0, bytesAdded), // Ensure non-negative
        document_count: Math.max(0, countChange), // Ensure non-negative
        external_document_count: isExternal ? Math.max(0, countChange) : 0, // Ensure non-negative
        last_calculated_at: new Date()
      });
    } else {
      // Update existing record
      const newBytesUsed = Math.max(0, storageUsage.total_bytes_used + bytesAdded);
      const newDocCount = Math.max(0, storageUsage.document_count + countChange);
      const newExternalDocCount = Math.max(
        0, 
        storageUsage.external_document_count + (isExternal ? countChange : 0)
      );

      await storageUsage.update({
        total_bytes_used: newBytesUsed,
        document_count: newDocCount,
        external_document_count: newExternalDocCount,
        last_calculated_at: new Date()
      });
    }

    return true;
  } catch (error) {
    console.error('Error updating storage usage:', error);
    return false;
  }
};

/**
 * Recalculates storage usage for a farm from scratch
 * @param {string} farmId - The farm ID
 * @returns {Promise<boolean>} Whether the recalculation was successful
 */
export const recalculateStorageUsage = async (farmId) => {
  try {
    // Get all documents for the farm
    const documents = await Document.findAll({
      where: { farm_id: farmId }
    });

    // Calculate total bytes used and document counts
    let totalBytesUsed = 0;
    let documentCount = documents.length;
    let externalDocumentCount = 0;

    for (const doc of documents) {
      if (doc.is_external) {
        externalDocumentCount++;
      } else {
        totalBytesUsed += doc.file_size;
      }
    }

    // Update or create storage usage record
    let storageUsage = await FarmStorageUsage.findOne({
      where: { farm_id: farmId }
    });

    if (!storageUsage) {
      storageUsage = await FarmStorageUsage.create({
        farm_id: farmId,
        total_bytes_used: totalBytesUsed,
        document_count: documentCount,
        external_document_count: externalDocumentCount,
        last_calculated_at: new Date()
      });
    } else {
      await storageUsage.update({
        total_bytes_used: totalBytesUsed,
        document_count: documentCount,
        external_document_count: externalDocumentCount,
        last_calculated_at: new Date()
      });
    }

    return true;
  } catch (error) {
    console.error('Error recalculating storage usage:', error);
    return false;
  }
};

/**
 * Saves an encrypted file to Digital Ocean Spaces (or filesystem as fallback)
 * @param {Buffer|string} fileData - The file data or path to temp file
 * @param {string} storagePath - The storage path relative to the uploads directory
 * @returns {Promise<{storagePath: string, encryptionDetails: {key: string, iv: string, method: string}}>}
 */
export const saveEncryptedFile = async (fileData, storagePath) => {
  try {
    // Encrypt the file
    const { encryptedData, key, iv, method } = await encryptFile(fileData);

    // Save the encrypted data
    await uploadToSpaces(encryptedData, storagePath);

    return {
      storagePath,
      encryptionDetails: {
        key,
        iv,
        method
      }
    };
  } catch (error) {
    console.error('Error saving encrypted file to Spaces, falling back to local storage:', error);

    // Fallback to local storage
    const fullPath = path.join(uploadsDir, storagePath);
    const dirPath = path.dirname(fullPath);

    // Encrypt the file
    const { encryptedData, key, iv, method } = await encryptFile(fileData);

    // Ensure the farm directory exists at the root level
    const pathParts = storagePath.split(path.sep);
    if (pathParts.length > 0) {
      const farmId = pathParts[0];
      const farmDir = path.join(uploadsDir, farmId);

      // Create farm directory if it doesn't exist
      if (farmId && !fs.existsSync(farmDir)) {
        await mkdir(farmDir, { recursive: true });
        console.log(`Created root directory for farm: ${farmId}`);
      }
    }

    // Create directory if it doesn't exist
    await mkdir(dirPath, { recursive: true });

    // Write the encrypted buffer to the file
    await writeFile(fullPath, encryptedData);

    return {
      storagePath: fullPath,
      encryptionDetails: {
        key,
        iv,
        method
      }
    };
  }
};

/**
 * Retrieves and decrypts a file from Digital Ocean Spaces (or filesystem as fallback)
 * @param {string} storagePath - The storage path relative to the uploads directory
 * @param {string} encryptedKey - The encrypted key
 * @param {string} iv - The initialization vector
 * @param {string} method - The encryption method used
 * @returns {Promise<Buffer>} The decrypted file data
 */
export const retrieveAndDecryptFile = async (storagePath, encryptedKey, iv, method) => {
  try {
    let encryptedData;

    // First try to get the file from Spaces
    try {
      const fileExists = await fileExistsInSpaces(storagePath);
      if (fileExists) {
        encryptedData = await downloadFromSpaces(storagePath);
      }
    } catch (error) {
      console.log('File not found in Spaces, trying local filesystem');
    }

    // If not found in Spaces, try local filesystem
    if (!encryptedData) {
      const fullPath = path.join(uploadsDir, storagePath);
      if (!fs.existsSync(fullPath)) {
        throw new Error(`File not found: ${fullPath}`);
      }
      encryptedData = await readFile(fullPath);
    }

    // Decrypt the file
    const decryptedData = await decryptFile(encryptedData, encryptedKey, iv, method);

    return decryptedData;
  } catch (error) {
    console.error('Error retrieving and decrypting file:', error);
    throw error;
  }
};

export default {
  validateFileType,
  generateStoragePath,
  saveFile,
  saveEncryptedFile,
  deleteFile,
  extractTextContent,
  readFileContent,
  checkStorageQuota,
  updateStorageUsage,
  recalculateStorageUsage,
  retrieveAndDecryptFile,
  ALLOWED_FILE_TYPES,
  DANGEROUS_EXTENSIONS
};
