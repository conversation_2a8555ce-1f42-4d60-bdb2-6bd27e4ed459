import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useHelp } from '../context/HelpContext';

const GettingStartedWidget: React.FC = () => {
  // Get the help context and provide default values for properties that might be null or undefined
  const {
    guides = [],
    loading = false,
    error = null,
    gettingStartedTasks = [],
    gettingStartedProgress = null,
    loadingProgress = false,
    fetchGettingStartedProgress = () => Promise.resolve()
  } = useHelp();
  const [gettingStartedGuides, setGettingStartedGuides] = useState<any[]>([]);
  const [expandedTask, setExpandedTask] = useState<string | null>(null);

  useEffect(() => {
    // Filter guides related to getting started
    if (guides && guides.length > 0) {
      const filteredGuides = guides.filter(
        guide => guide.category === 'Getting Started' || guide.tags.includes('getting-started')
      ).slice(0, 5); // Limit to 5 guides

      setGettingStartedGuides(filteredGuides);
    }
  }, [guides]);

  useEffect(() => {
    // Fetch getting started progress when component mounts
    fetchGettingStartedProgress();
  }, [fetchGettingStartedProgress]);

  // Toggle task description visibility
  const toggleTaskDescription = (taskId: string) => {
    if (expandedTask === taskId) {
      setExpandedTask(null);
    } else {
      setExpandedTask(taskId);
    }
  };

  return (
    <div className="p-4">
      <div className="mb-4">
        <h3 className="text-md font-medium text-gray-900 mb-2">Welcome to nxtAcre!</h3>
        <p className="text-sm text-gray-600">
          Complete these tasks to get the most out of your farm management platform. Track your progress and explore key features.
        </p>

      </div>

      {/* Setup Progress Section */}
      {loadingProgress ? (
        <div className="flex justify-center items-center py-2 mb-4">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600"></div>
          <span className="ml-2 text-xs text-gray-500">Loading progress...</span>
        </div>
      ) : gettingStartedProgress ? (
        <div className="mb-4">
          <div className="flex justify-between items-center mb-1">
            <h4 className="text-sm font-medium text-gray-800">Setup Progress</h4>
            <span className="text-xs font-medium text-gray-600">
              {gettingStartedProgress.completed || 0} of {gettingStartedProgress.total || 0} complete
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
              className="bg-primary-600 h-2.5 rounded-full"
              style={{ width: `${gettingStartedProgress.percentage || 0}%` }}
            ></div>
          </div>
          {gettingStartedTasks.length > 0 ? (
            <div className="mt-3">
              <h4 className="text-sm font-medium text-gray-800 mb-2">Recommended Tasks</h4>
              <ul className="space-y-2">
                {gettingStartedTasks.slice(0, 5).map(task => (
                  <li key={task.id} className="bg-white border border-gray-200 rounded-md overflow-hidden">
                    <div
                      className="flex items-start p-2 cursor-pointer hover:bg-gray-50"
                      onClick={() => toggleTaskDescription(task.id)}
                    >
                      {task.completed ? (
                        <svg className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      ) : (
                        <svg className="w-5 h-5 text-primary-600 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                      )}
                      <div className="flex-grow">
                        <span className={`text-sm font-medium ${task.completed ? 'text-gray-500' : 'text-gray-800'}`}>
                          {task.title}
                        </span>
                        {expandedTask === task.id && (
                          <p className="text-xs text-gray-600 mt-1">{task.description}</p>
                        )}
                      </div>
                      <svg
                        className={`w-4 h-4 text-gray-400 ml-2 mt-1 transform transition-transform ${expandedTask === task.id ? 'rotate-180' : ''}`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                    {expandedTask === task.id && (
                      <div className="px-2 pb-2">
                        <Link
                          to={task.linkPath}
                          className="text-xs bg-primary-600 text-white py-1 px-3 rounded-md inline-block hover:bg-primary-700"
                        >
                          Go to {task.title.toLowerCase()}
                        </Link>
                      </div>
                    )}
                  </li>
                ))}
              </ul>
              {gettingStartedTasks.length > 5 && (
                <Link to="/help/getting-started" className="text-xs text-primary-600 hover:underline mt-3 inline-block">
                  View all tasks ({gettingStartedTasks.length})
                </Link>
              )}
            </div>
          ) : (
            <div className="text-sm text-gray-500 mt-2">
              No getting started tasks available. Please check back later.
            </div>
          )}
        </div>
      ) : (
        <div className="text-sm text-gray-500 mb-4 p-3 bg-gray-50 border border-gray-200 rounded-md">
          <div className="flex items-center">
            <svg className="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Start exploring the platform by completing the recommended tasks.
          </div>
          <button
            onClick={() => fetchGettingStartedProgress()}
            className="mt-2 text-xs bg-primary-600 text-white py-1 px-3 rounded-md hover:bg-primary-700"
          >
            Load Tasks
          </button>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
          <span className="ml-2 text-gray-500">Loading guides...</span>
        </div>
      ) : error ? (
        <div className="text-sm text-red-500 mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-center">
            <svg className="w-4 h-4 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            {error}
          </div>
          <button
            onClick={() => fetchGettingStartedProgress()}
            className="mt-2 text-xs bg-red-600 text-white py-1 px-3 rounded-md hover:bg-red-700"
          >
            Try Again
          </button>
        </div>
      ) : (
        <>
          {gettingStartedGuides.length > 0 ? (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-800 mb-2">Helpful Guides</h4>
              <ul className="space-y-2">
                {gettingStartedGuides.map(guide => (
                  <li key={guide.id} className="text-sm">
                    <Link
                      to={`/help/guides/${guide.slug}`}
                      className="text-primary-600 hover:text-primary-800 hover:underline flex items-center"
                    >
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                      {guide.title}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ) : null}
        </>
      )}

      <div className="bg-blue-50 p-3 rounded-lg mb-4">
        <h4 className="text-sm font-medium text-gray-800 mb-1">Explore Key Features</h4>
        <p className="text-xs text-gray-600 mb-2">
          Discover these important areas to make the most of nxtAcre:
        </p>
        <div className="grid grid-cols-2 gap-2">
          <Link
            to="/dashboard"
            className="bg-white hover:bg-gray-50 p-2 rounded border border-gray-200 flex flex-col items-center text-center"
          >
            <svg className="w-5 h-5 text-primary-600 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
            </svg>
            <h3 className="text-xs font-medium">Dashboard</h3>
          </Link>
          <Link
            to="/fields"
            className="bg-white hover:bg-gray-50 p-2 rounded border border-gray-200 flex flex-col items-center text-center"
          >
            <svg className="w-5 h-5 text-green-600 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
            </svg>
            <h3 className="text-xs font-medium">Fields</h3>
          </Link>
          <Link
            to="/inventory"
            className="bg-white hover:bg-gray-50 p-2 rounded border border-gray-200 flex flex-col items-center text-center"
          >
            <svg className="w-5 h-5 text-yellow-600 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
            </svg>
            <h3 className="text-xs font-medium">Inventory</h3>
          </Link>
          <Link
            to="/finances"
            className="bg-white hover:bg-gray-50 p-2 rounded border border-gray-200 flex flex-col items-center text-center"
          >
            <svg className="w-5 h-5 text-blue-600 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="text-xs font-medium">Finances</h3>
          </Link>
        </div>
      </div>

      <div className="mt-2 text-center">
        <Link
          to="/help"
          className="text-primary-600 hover:text-primary-800 text-sm font-medium"
        >
          View All Help Resources
        </Link>
      </div>
    </div>
  );
};

export default GettingStartedWidget;
