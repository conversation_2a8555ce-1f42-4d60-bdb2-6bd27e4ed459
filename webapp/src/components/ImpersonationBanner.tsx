import React from 'react';
import { useAuth } from '../context/AuthContext';

const ImpersonationBanner: React.FC = () => {
  const { isImpersonating, user, endImpersonation } = useAuth();

  if (!isImpersonating) {
    return null;
  }

  return (
    <div className="bg-yellow-500 text-white py-2 px-4 flex justify-between items-center">
      <div>
        <span className="font-bold">Impersonating User: </span>
        <span>{user?.firstName} {user?.lastName} ({user?.email})</span>
      </div>
      <button
        onClick={() => endImpersonation()}
        className="bg-white text-yellow-700 px-4 py-1 rounded-md hover:bg-yellow-100 transition-colors"
      >
        Return to Admin Account
      </button>
    </div>
  );
};

export default ImpersonationBanner;