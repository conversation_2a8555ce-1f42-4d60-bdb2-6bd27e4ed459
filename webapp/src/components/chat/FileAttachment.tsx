import React from 'react';
import { Attachment } from '../../context/ChatContext';

interface FileAttachmentProps {
  attachment: Attachment;
}

/**
 * Component for displaying file attachments in messages
 */
const FileAttachment: React.FC<FileAttachmentProps> = ({ attachment }) => {
  // Determine if the file is an image that can be previewed
  const isImage = attachment.file_type.startsWith('image/');
  
  // Determine file icon based on file type
  const getFileIcon = () => {
    if (attachment.file_type.includes('pdf')) {
      return '📄';
    } else if (attachment.file_type.includes('word') || attachment.file_type.includes('document')) {
      return '📝';
    } else if (attachment.file_type.includes('excel') || attachment.file_type.includes('spreadsheet')) {
      return '📊';
    } else if (attachment.file_type.includes('powerpoint') || attachment.file_type.includes('presentation')) {
      return '📑';
    } else if (attachment.file_type.includes('zip') || attachment.file_type.includes('compressed')) {
      return '🗜️';
    } else if (attachment.file_type.includes('audio')) {
      return '🔊';
    } else if (attachment.file_type.includes('video')) {
      return '🎬';
    } else {
      return '📎';
    }
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) {
      return bytes + ' B';
    } else if (bytes < 1024 * 1024) {
      return (bytes / 1024).toFixed(1) + ' KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    } else {
      return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
    }
  };

  return (
    <div className="mt-2 mb-1">
      {isImage ? (
        // Image preview
        <div className="relative">
          <a 
            href={attachment.file_url} 
            target="_blank" 
            rel="noopener noreferrer"
            className="block"
          >
            <img 
              src={attachment.thumbnail_url || attachment.file_url} 
              alt={attachment.file_name}
              className="max-w-full max-h-48 rounded border"
            />
          </a>
          <div className="text-xs mt-1 text-gray-500">
            {attachment.file_name} ({formatFileSize(attachment.file_size)})
          </div>
        </div>
      ) : (
        // Non-image file
        <a 
          href={attachment.file_url} 
          target="_blank" 
          rel="noopener noreferrer"
          className="flex items-center p-2 border rounded bg-gray-50 hover:bg-gray-100"
        >
          <span className="text-2xl mr-2">{getFileIcon()}</span>
          <div className="flex-1 overflow-hidden">
            <div className="truncate font-medium">{attachment.file_name}</div>
            <div className="text-xs text-gray-500">{formatFileSize(attachment.file_size)}</div>
          </div>
        </a>
      )}
    </div>
  );
};

export default FileAttachment;