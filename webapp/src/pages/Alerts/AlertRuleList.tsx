import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { getAlertRules, deleteAlertRule, AlertRule, AlertCondition, AlertAction } from '../../services/alertService';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';

const AlertRuleList: React.FC = () => {
  const [rules, setRules] = useState<AlertRule[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { selectedFarm } = useFarm();

  useEffect(() => {
    if (selectedFarm) {
      fetchAlertRules();
    }
  }, [selectedFarm]);

  const fetchAlertRules = async () => {
    if (!selectedFarm) return;

    try {
      setIsLoading(true);
      setError(null);

      const rulesData = await getAlertRules(selectedFarm.id);
      setRules(rulesData);
    } catch (err) {
      console.error('Error fetching alert rules:', err);
      setError('Failed to load alert rules. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteRule = async (ruleId: string) => {
    if (!window.confirm('Are you sure you want to delete this alert rule?')) {
      return;
    }

    if (!selectedFarm) {
      setError('No farm selected. Please select a farm and try again.');
      return;
    }

    try {
      await deleteAlertRule(ruleId, selectedFarm.id);
      setRules(rules.filter(rule => rule.id !== ruleId));
    } catch (err) {
      console.error('Error deleting alert rule:', err);
      setError('Failed to delete alert rule. Please try again later.');
    }
  };

  const formatConditions = (conditions: AlertCondition[]) => {
    if (!conditions || conditions.length === 0) return 'No conditions';

    return conditions.map((condition, index) => {
      let conditionText = '';

      switch (condition.type) {
        case 'threshold':
          conditionText = `${condition.parameter} ${condition.operator} ${condition.value}`;
          break;
        case 'schedule':
          conditionText = `Scheduled at ${condition.value}`;
          break;
        case 'status_change':
          conditionText = `Status changes to ${condition.value}`;
          break;
        case 'weather':
          conditionText = `Weather ${condition.parameter} ${condition.operator} ${condition.value}`;
          break;
        case 'inventory_level':
          conditionText = `Inventory level ${condition.operator} ${condition.value}`;
          break;
        default:
          conditionText = `${condition.type}: ${condition.parameter} ${condition.operator} ${condition.value}`;
      }

      return (
        <div key={index} className="text-sm">
          {conditionText}
        </div>
      );
    });
  };

  const formatActions = (actions: AlertAction[]) => {
    if (!actions || actions.length === 0) return 'No actions';

    return actions.map((action, index) => {
      let actionText = '';

      switch (action.type) {
        case 'notification':
          actionText = 'Send in-app notification';
          break;
        case 'email':
          actionText = `Send email to ${action.recipients?.join(', ') || 'user'}`;
          break;
        case 'sms':
          actionText = `Send SMS to ${action.recipients?.join(', ') || 'user'}`;
          break;
        case 'task_creation':
          actionText = `Create task: ${action.taskDetails?.title || 'Untitled'}`;
          break;
        default:
          actionText = action.type;
      }

      return (
        <div key={index} className="text-sm">
          {actionText}
        </div>
      );
    });
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-800">Alert Rules</h1>
          <Link
            to="/alerts/rules/new"
            className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
          >
            Create New Rule
          </Link>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {rules.length === 0 ? (
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <p className="text-gray-600">No alert rules found. Create your first rule to get started.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-6">
            {rules.map(rule => (
              <div key={rule.id} className="bg-white rounded-lg shadow-md p-6">
                <div className="flex justify-between items-start">
                  <div>
                    <h2 className="text-xl font-semibold text-gray-800">{rule.name}</h2>
                    {rule.description && (
                      <p className="text-gray-600 mt-1">{rule.description}</p>
                    )}
                  </div>
                  <div className="flex space-x-2">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      rule.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {rule.enabled ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <div>
                    <h3 className="font-medium text-gray-700 mb-2">Conditions</h3>
                    <div className="bg-gray-50 p-3 rounded">
                      {formatConditions(rule.conditions as AlertCondition[])}
                    </div>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-700 mb-2">Actions</h3>
                    <div className="bg-gray-50 p-3 rounded">
                      {formatActions(rule.actions as AlertAction[])}
                    </div>
                  </div>
                </div>

                <div className="flex justify-end mt-4 space-x-2">
                  <Link
                    to={`/alerts/rules/${rule.id}/edit`}
                    className="text-blue-600 hover:text-blue-800 px-3 py-1 border border-blue-600 rounded hover:bg-blue-50"
                  >
                    Edit
                  </Link>
                  <button
                    onClick={() => handleDeleteRule(rule.id)}
                    className="text-red-600 hover:text-red-800 px-3 py-1 border border-red-600 rounded hover:bg-red-50"
                  >
                    Delete
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </Layout>
  );
};

export default AlertRuleList;
