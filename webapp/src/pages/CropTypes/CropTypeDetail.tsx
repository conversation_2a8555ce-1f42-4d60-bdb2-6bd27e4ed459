import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface CropType {
  id: string;
  farm_id: string;
  name: string;
  description: string;
  growing_season: string;
  days_to_maturity: number;
  planting_depth: number;
  row_spacing: number;
  plant_spacing: number;
  ideal_soil_ph: number;
  ideal_temperature: number;
  created_at: string;
  updated_at: string;
}

const CropTypeDetail = () => {
  const { cropTypeId } = useParams<{ cropTypeId: string }>();
  const navigate = useNavigate();

  const [cropType, setCropType] = useState<CropType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [farm, setFarm] = useState<{ id: string; name: string } | null>(null);

  // Fetch crop type data
  useEffect(() => {
    const fetchCropType = async () => {
      if (!cropTypeId) return;

      setLoading(true);
      setError(null);

      try {
        // Fetch crop type details
        const cropTypeResponse = await axios.get(`${API_URL}/crop-types/${cropTypeId}`);
        setCropType(cropTypeResponse.data.cropType || cropTypeResponse.data);

        // Fetch farm details
        const farmResponse = await axios.get(`${API_URL}/farms/${cropTypeResponse.data.farm_id}`);
        setFarm(farmResponse.data);
      } catch (err: any) {
        console.error('Error fetching crop type details:', err);
        setError('Failed to load crop type details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchCropType();
  }, [cropTypeId]);

  // Handle crop type deletion
  const handleDeleteCropType = async () => {
    if (!window.confirm('Are you sure you want to delete this crop type?')) {
      return;
    }

    try {
      await axios.delete(`${API_URL}/crop-types/${cropTypeId}`);
      navigate('/crop-types');
    } catch (err: any) {
      console.error('Error deleting crop type:', err);
      setError('Failed to delete crop type. Please try again later.');
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading crop type details...</p>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
        <div className="flex justify-center mt-4">
          <Link
            to="/crop-types"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Crop Types
          </Link>
        </div>
      </Layout>
    );
  }

  if (!cropType) {
    return (
      <Layout>
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">Crop type not found.</span>
        </div>
        <div className="flex justify-center mt-4">
          <Link
            to="/crop-types"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Crop Types
          </Link>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">{cropType.name}</h1>
        <div className="flex space-x-2">
          <Link
            to={`/crop-types/${cropTypeId}/edit`}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Edit
          </Link>
          <button
            onClick={handleDeleteCropType}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Delete
          </button>
          <Link
            to="/crop-types"
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Crop Types
          </Link>
        </div>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Crop Type Details</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">Details and information about the crop type.</p>
        </div>
        <div className="border-t border-gray-200">
          <dl>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Farm</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {farm ? farm.name : 'Unknown Farm'}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Description</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {cropType.description || 'No description provided'}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Growing Season</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {cropType.growing_season ? cropType.growing_season.charAt(0).toUpperCase() + cropType.growing_season.slice(1) : 'Not specified'}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Days to Maturity</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {cropType.days_to_maturity ? `${cropType.days_to_maturity} days` : 'Not specified'}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Planting Depth</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {cropType.planting_depth ? `${cropType.planting_depth} inches` : 'Not specified'}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Row Spacing</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {cropType.row_spacing ? `${cropType.row_spacing} inches` : 'Not specified'}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Plant Spacing</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {cropType.plant_spacing ? `${cropType.plant_spacing} inches` : 'Not specified'}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Ideal Soil pH</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {cropType.ideal_soil_ph || 'Not specified'}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Ideal Temperature</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {cropType.ideal_temperature ? `${cropType.ideal_temperature}°F` : 'Not specified'}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Created</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {cropType.created_at ? formatDate(cropType.created_at) : 'Unknown'}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {cropType.updated_at ? formatDate(cropType.updated_at) : 'Unknown'}
              </dd>
            </div>
          </dl>
        </div>
      </div>

      {/* Related Crops Section */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Related Information</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            Use this crop type information when planning your planting schedule and field operations.
          </p>
        </div>
        <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
          <div className="prose prose-sm text-gray-500 max-w-none">
            <p>
              Crop types define the characteristics and requirements for growing specific crops. 
              Use this information to:
            </p>
            <ul className="list-disc pl-5 mt-2">
              <li>Plan your planting schedule based on growing season and days to maturity</li>
              <li>Configure planting equipment with the correct depth and spacing settings</li>
              <li>Prepare soil conditions to match the ideal pH for this crop type</li>
              <li>Monitor temperature conditions to ensure optimal growing environment</li>
            </ul>
            <p className="mt-4">
              You can reference this crop type when creating crops in the system to ensure consistent 
              growing practices across your farm.
            </p>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default CropTypeDetail;