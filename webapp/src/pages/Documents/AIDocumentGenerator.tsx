import { useState, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

const documentTypes = [
  { value: 'agreement', label: 'Agreement' },
  { value: 'contract', label: 'Contract' },
  { value: 'lease', label: 'Lease' },
  { value: 'invoice', label: 'Invoice' },
  { value: 'receipt', label: 'Receipt' },
  { value: 'letter', label: 'Letter' },
  { value: 'other', label: 'Other' }
];

const AIDocumentGenerator = () => {
  const [prompt, setPrompt] = useState('');
  const [documentType, setDocumentType] = useState('agreement');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [generatedDocument, setGeneratedDocument] = useState<any>(null);
  const [showSaveAsTemplateModal, setShowSaveAsTemplateModal] = useState(false);
  const [templateTitle, setTemplateTitle] = useState('');
  const [templateDescription, setTemplateDescription] = useState('');
  const [savingTemplate, setSavingTemplate] = useState(false);
  const [saveTemplateSuccess, setSaveTemplateSuccess] = useState<string | null>(null);
  const [saveTemplateError, setSaveTemplateError] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();
  const navigate = useNavigate();

  const handleGenerateDocument = async () => {
    if (!prompt.trim()) {
      setError('Please enter a prompt for the document');
      return;
    }

    if (!currentFarm?.id) {
      setError('No farm selected. Please select a farm to generate a document.');
      return;
    }

    setLoading(true);
    setError(null);
    setGeneratedDocument(null);

    try {
      const response = await axios.post(
        `${API_URL}/ai-document-generation/generate`,
        {
          prompt,
          documentType,
          farmId: currentFarm.id
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        }
      );

      setGeneratedDocument(response.data.document);
    } catch (err: any) {
      console.error('Error generating document:', err);
      setError(err.response?.data?.message || 'Failed to generate document. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveAsTemplate = async () => {
    if (!generatedDocument) {
      setSaveTemplateError('No document to save as template');
      return;
    }

    if (!templateTitle.trim()) {
      setSaveTemplateError('Please enter a title for the template');
      return;
    }

    setSavingTemplate(true);
    setSaveTemplateError(null);
    setSaveTemplateSuccess(null);

    try {
      const response = await axios.post(
        `${API_URL}/ai-document-generation/save-as-template`,
        {
          documentId: generatedDocument.id,
          farmId: currentFarm?.id,
          title: templateTitle,
          description: templateDescription
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        }
      );

      setSaveTemplateSuccess('Document saved as template successfully');
      
      // Close modal after a short delay
      setTimeout(() => {
        setShowSaveAsTemplateModal(false);
        setSaveTemplateSuccess(null);
      }, 2000);
    } catch (err: any) {
      console.error('Error saving as template:', err);
      setSaveTemplateError(err.response?.data?.message || 'Failed to save as template. Please try again later.');
    } finally {
      setSavingTemplate(false);
    }
  };

  const handleViewDocument = () => {
    if (generatedDocument) {
      navigate(`/documents/signing/view/${generatedDocument.id}`);
    }
  };

  const handleEditDocument = () => {
    if (generatedDocument) {
      navigate(`/documents/signing/edit/${generatedDocument.id}`);
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">AI Document Generator</h1>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="max-w-3xl mx-auto">
            {/* Document generation form */}
            {!generatedDocument && (
              <div className="space-y-6">
                <div>
                  <label htmlFor="documentType" className="block text-sm font-medium text-gray-700">
                    Document Type
                  </label>
                  <select
                    id="documentType"
                    value={documentType}
                    onChange={(e) => setDocumentType(e.target.value)}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                  >
                    {documentTypes.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label htmlFor="prompt" className="block text-sm font-medium text-gray-700">
                    Describe the document you need
                  </label>
                  <div className="mt-1">
                    <textarea
                      id="prompt"
                      name="prompt"
                      rows={6}
                      value={prompt}
                      onChange={(e) => setPrompt(e.target.value)}
                      className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      placeholder="Example: Create a lease agreement for renting out 50 acres of farmland for corn production for 3 years at $200 per acre per year, with an option to renew for 2 additional years."
                    />
                  </div>
                  <p className="mt-2 text-sm text-gray-500">
                    Be specific about the type of document, key terms, parties involved, and any special clauses you want to include.
                  </p>
                </div>

                {error && (
                  <div className="rounded-md bg-red-50 p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">{error}</h3>
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex justify-end">
                  <button
                    type="button"
                    onClick={handleGenerateDocument}
                    disabled={loading}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
                  >
                    {loading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Generating Document...
                      </>
                    ) : (
                      'Generate Document'
                    )}
                  </button>
                </div>
              </div>
            )}

            {/* Generated document display */}
            {generatedDocument && (
              <div className="space-y-6">
                <div className="bg-gray-50 p-4 rounded-md">
                  <h2 className="text-lg font-medium text-gray-900">{generatedDocument.title}</h2>
                  <p className="mt-1 text-sm text-gray-500">
                    Type: {documentTypes.find(t => t.value === generatedDocument.documentType)?.label || generatedDocument.documentType}
                  </p>
                </div>

                <div className="border border-gray-300 rounded-md p-4 bg-white">
                  <pre className="whitespace-pre-wrap font-sans text-sm text-gray-900">{generatedDocument.content}</pre>
                </div>

                <div className="flex justify-between">
                  <div>
                    <button
                      type="button"
                      onClick={() => setShowSaveAsTemplateModal(true)}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Save as Template
                    </button>
                  </div>
                  <div className="flex space-x-3">
                    <button
                      type="button"
                      onClick={handleEditDocument}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Edit Document
                    </button>
                    <button
                      type="button"
                      onClick={handleViewDocument}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      View Document
                    </button>
                  </div>
                </div>

                <div className="mt-6 border-t border-gray-200 pt-6">
                  <button
                    type="button"
                    onClick={() => {
                      setGeneratedDocument(null);
                      setError(null);
                    }}
                    className="text-primary-600 hover:text-primary-500"
                  >
                    Generate Another Document
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Save as Template Modal */}
      {showSaveAsTemplateModal && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">Save as Template</h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        This will save the generated document as a template that you can reuse later.
                      </p>
                      <div className="mt-4 space-y-4">
                        <div>
                          <label htmlFor="templateTitle" className="block text-sm font-medium text-gray-700">
                            Template Title <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="text"
                            id="templateTitle"
                            value={templateTitle}
                            onChange={(e) => setTemplateTitle(e.target.value)}
                            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                            placeholder="Enter a title for this template"
                          />
                        </div>
                        <div>
                          <label htmlFor="templateDescription" className="block text-sm font-medium text-gray-700">
                            Description
                          </label>
                          <textarea
                            id="templateDescription"
                            value={templateDescription}
                            onChange={(e) => setTemplateDescription(e.target.value)}
                            rows={3}
                            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                            placeholder="Enter a description for this template"
                          />
                        </div>
                      </div>
                    </div>

                    {saveTemplateError && (
                      <div className="mt-4 rounded-md bg-red-50 p-4">
                        <div className="flex">
                          <div className="flex-shrink-0">
                            <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <div className="ml-3">
                            <h3 className="text-sm font-medium text-red-800">{saveTemplateError}</h3>
                          </div>
                        </div>
                      </div>
                    )}

                    {saveTemplateSuccess && (
                      <div className="mt-4 rounded-md bg-green-50 p-4">
                        <div className="flex">
                          <div className="flex-shrink-0">
                            <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <div className="ml-3">
                            <h3 className="text-sm font-medium text-green-800">{saveTemplateSuccess}</h3>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={handleSaveAsTemplate}
                  disabled={savingTemplate}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
                >
                  {savingTemplate ? 'Saving...' : 'Save as Template'}
                </button>
                <button
                  type="button"
                  onClick={() => setShowSaveAsTemplateModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default AIDocumentGenerator;