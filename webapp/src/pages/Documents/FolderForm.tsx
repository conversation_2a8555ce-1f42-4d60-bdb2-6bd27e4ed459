import { useState, useEffect, useContext } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface Folder {
  id: string;
  name: string;
  description: string;
  parent_folder_id: string | null;
}

const FolderForm = () => {
  const { id } = useParams<{ id: string }>();
  const isEditMode = !!id;

  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [parentFolderId, setParentFolderId] = useState<string | null>(null);
  const [folders, setFolders] = useState<Folder[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const navigate = useNavigate();

  // Fetch folders for dropdown
  useEffect(() => {
    const fetchFolders = async () => {
      try {
        const response = await axios.get(
          `${API_URL}/documents/farm/${user?.farm_id}/folders`,
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('token')}`
            }
          }
        );

        setFolders(response.data || []);
      } catch (err: any) {
        console.error('Error fetching folders:', err);
        setError('Failed to load folders. Please try again later.');
      }
    };

    if (user?.farm_id) {
      fetchFolders();
    }
  }, [user?.farm_id]);

  // Fetch folder details if in edit mode
  useEffect(() => {
    const fetchFolder = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`${API_URL}/documents/folders/${id}`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        });

        const folder = response.data;
        setName(folder.name);
        setDescription(folder.description || '');
        setParentFolderId(folder.parent_folder_id);
      } catch (err: any) {
        console.error('Error fetching folder:', err);
        setError('Failed to load folder. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (isEditMode && id) {
      fetchFolder();
    }
  }, [isEditMode, id]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      if (isEditMode) {
        // Update existing folder
        await axios.put(
          `${API_URL}/documents/folders/${id}`,
          {
            name,
            description,
            parentFolderId: parentFolderId || null
          },
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('token')}`
            }
          }
        );

        navigate('/documents/folders');
      } else {
        // Create new folder
        await axios.post(
          `${API_URL}/documents/farm/${user?.farm_id}/folders`,
          {
            name,
            description,
            parentFolderId: parentFolderId || null
          },
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('token')}`
            }
          }
        );

        navigate('/documents/folders');
      }
    } catch (err: any) {
      console.error('Error saving folder:', err);
      setError(err.response?.data?.error || 'Failed to save folder. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Link
            to="/documents/folders"
            className="mr-4 text-gray-500 hover:text-gray-700"
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              />
            </svg>
          </Link>
          <h1 className="text-2xl font-bold text-gray-900">
            {isEditMode ? 'Edit Folder' : 'Create Folder'}
          </h1>
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div className="mb-4 p-4 text-sm text-red-700 bg-red-100 rounded-md">
          {error}
        </div>
      )}

      {/* Loading state */}
      {loading && !isEditMode ? (
        <div className="flex justify-center items-center h-64">
          <svg
            className="animate-spin h-8 w-8 text-primary-500"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="bg-white shadow overflow-hidden sm:rounded-lg p-6">
          <div className="grid grid-cols-1 gap-6">
            {/* Folder name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Folder Name
              </label>
              <input
                type="text"
                name="name"
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              />
            </div>

            {/* Folder description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                name="description"
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={3}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Parent folder selection */}
            <div>
              <label htmlFor="parentFolder" className="block text-sm font-medium text-gray-700">
                Parent Folder
              </label>
              <select
                id="parentFolder"
                name="parentFolder"
                value={parentFolderId || ''}
                onChange={(e) => setParentFolderId(e.target.value || null)}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="">Root (No Parent)</option>
                {folders
                  .filter(folder => !isEditMode || folder.id !== id) // Exclude current folder in edit mode
                  .map((folder) => (
                    <option key={folder.id} value={folder.id}>
                      {folder.name}
                    </option>
                  ))}
              </select>
            </div>

            {/* Submit button */}
            <div className="flex justify-end">
              <Link
                to="/documents/folders"
                className="mr-3 inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={loading}
                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
              >
                {loading ? (
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                ) : null}
                {isEditMode ? 'Save Changes' : 'Create Folder'}
              </button>
            </div>
          </div>
        </form>
      )}
    </Layout>
  );
};

export default FolderForm;
