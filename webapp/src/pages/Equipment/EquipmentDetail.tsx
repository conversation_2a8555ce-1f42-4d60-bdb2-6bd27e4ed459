import { useState, useEffect } from 'react';
import { useParams, Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import Layout from '../../components/Layout';
import EquipmentTelematics from '../../components/EquipmentTelematics';
import EquipmentIsobus from '../../components/EquipmentIsobus';
import EquipmentOem from '../../components/EquipmentOem';
import { API_URL } from '../../config';

interface Equipment {
  id: string;
  farm_id: string;
  name: string;
  type: string;
  manufacturer: string;
  model: string;
  year: number;
  purchase_date: string;
  purchase_cost: number;
  current_value: number;
  status: string;
  notes: string;
  created_at: string;
  updated_at: string;
}

interface Farm {
  id: string;
  name: string;
}

interface Product {
  id: string;
  name: string;
  price: number;
  unit: string;
  created_at: string;
}

const EquipmentDetail = () => {
  const { equipmentId } = useParams<{ equipmentId: string }>();
  const navigate = useNavigate();

  const [equipment, setEquipment] = useState<Equipment | null>(null);
  const [farm, setFarm] = useState<Farm | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);


  // Fetch equipment data
  useEffect(() => {
    const fetchEquipment = async () => {
      if (!equipmentId) return;

      setLoading(true);
      setError(null);

      try {
        // Fetch equipment details
        const equipmentResponse = await axios.get(`${API_URL}/equipment/${equipmentId}`);
        setEquipment(equipmentResponse.data);

        // Fetch farm details
        const farmResponse = await axios.get(`${API_URL}/farms/${equipmentResponse.data.farm_id}`);
        setFarm(farmResponse.data);

        // Fetch products associated with this equipment
        const productsResponse = await axios.get(`${API_URL}/products/farm/${equipmentResponse.data.farm_id}`);
        // Filter products that are associated with this equipment
        const equipmentProducts = productsResponse.data.filter((product: any) => 
          product.type === 'equipment' && product.source_id === equipmentId
        );
        setProducts(equipmentProducts);
      } catch (err: any) {
        console.error('Error fetching equipment details:', err);
        setError('Failed to load equipment details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchEquipment();
  }, [equipmentId]);

  // Handle equipment deletion
  const handleDeleteEquipment = async () => {
    if (!window.confirm('Are you sure you want to delete this equipment?')) {
      return;
    }

    try {
      await axios.delete(`${API_URL}/equipment/${equipmentId}`);
      navigate('/equipment');
    } catch (err: any) {
      console.error('Error deleting equipment:', err);
      setError('Failed to delete equipment. Please try again later.');
    }
  };

  // Create a product from this equipment
  const handleCreateProduct = async () => {
    try {
      const response = await axios.post(`${API_URL}/products/from-equipment/${equipmentId}`, {
        price: 0, // Default price, can be updated later
        unit: 'unit' // Default unit for equipment
      });

      // Add the new product to the products list
      setProducts([...products, response.data]);

      alert('Product created successfully!');
    } catch (err: any) {
      console.error('Error creating product from equipment:', err);
      setError('Failed to create product. Please try again later.');
    }
  };

  // Format currency
  const formatCurrency = (amount: number | null) => {
    if (amount === null) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading equipment details...</p>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
        <div className="flex justify-center mt-4">
          <Link
            to="/equipment"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Equipment
          </Link>
        </div>
      </Layout>
    );
  }

  if (!equipment) {
    return (
      <Layout>
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">Equipment not found.</span>
        </div>
        <div className="flex justify-center mt-4">
          <Link
            to="/equipment"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Equipment
          </Link>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">{equipment.name}</h1>
        <div className="flex space-x-2">
          <button
            onClick={handleCreateProduct}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            Create Product
          </button>
          <Link
            to={`/equipment/${equipmentId}/edit`}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Edit
          </Link>
          <button
            onClick={handleDeleteEquipment}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Delete
          </button>
          <Link
            to="/equipment"
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Equipment
          </Link>
        </div>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Equipment Details</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">Details and information about the equipment.</p>
        </div>
        <div className="border-t border-gray-200">
          <dl>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Farm</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {farm ? farm.name : 'Unknown Farm'}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Type</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {equipment.type ? equipment.type.charAt(0).toUpperCase() + equipment.type.slice(1) : 'Not specified'}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Manufacturer</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {equipment.manufacturer || 'Not specified'}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Model</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {equipment.model || 'Not specified'}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Year</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {equipment.year || 'Not specified'}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Purchase Date</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {equipment.purchase_date ? formatDate(equipment.purchase_date) : 'Not specified'}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Purchase Cost</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {equipment.purchase_cost ? formatCurrency(equipment.purchase_cost) : 'Not specified'}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Current Value</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {equipment.current_value ? formatCurrency(equipment.current_value) : 'Not specified'}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Status</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                  equipment.status === 'active' ? 'bg-green-100 text-green-800' : 
                  equipment.status === 'maintenance' ? 'bg-yellow-100 text-yellow-800' : 
                  equipment.status === 'retired' ? 'bg-red-100 text-red-800' : 
                  equipment.status === 'sold' ? 'bg-blue-100 text-blue-800' : 
                  'bg-gray-100 text-gray-800'
                }`}>
                  {equipment.status.charAt(0).toUpperCase() + equipment.status.slice(1)}
                </span>
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Notes</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {equipment.notes || 'No notes'}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Created</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {formatDate(equipment.created_at)}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {formatDate(equipment.updated_at)}
              </dd>
            </div>
          </dl>
        </div>
      </div>

      {/* Products Section */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <div>
            <h3 className="text-lg leading-6 font-medium text-gray-900">Products</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">Products created from this equipment.</p>
          </div>
          <button
            onClick={handleCreateProduct}
            className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            Create Product
          </button>
        </div>
        <div className="border-t border-gray-200">
          {products.length === 0 ? (
            <div className="px-4 py-5 sm:px-6 text-center">
              <p className="text-sm text-gray-500">No products have been created from this equipment yet.</p>
            </div>
          ) : (
            <ul className="divide-y divide-gray-200">
              {products.map(product => (
                <li key={product.id} className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-primary-600">{product.name}</p>
                      <p className="text-sm text-gray-500">
                        Price: {formatCurrency(product.price)} / {product.unit || 'unit'}
                      </p>
                      <p className="text-xs text-gray-400">
                        Created: {formatDate(product.created_at)}
                      </p>
                    </div>
                    <Link
                      to={`/products/${product.id}`}
                      className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      View Product
                    </Link>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>

      {/* Telematics Section */}
      {equipmentId && <EquipmentTelematics equipmentId={equipmentId} />}

      {/* ISOBUS Section */}
      {equipmentId && <EquipmentIsobus equipmentId={equipmentId} />}

      {/* OEM Integrations Section */}
      {equipmentId && equipment && <EquipmentOem equipmentId={equipmentId} manufacturer={equipment.manufacturer} />}
    </Layout>
  );
};

export default EquipmentDetail;
