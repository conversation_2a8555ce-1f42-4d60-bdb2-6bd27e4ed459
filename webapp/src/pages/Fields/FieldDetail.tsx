import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';
import { getFieldDataGov, FieldDataGov, getFieldRecommendedCrops, getFieldConservationPractices, getFieldHistoricalYieldData, getAllFieldData, AllFieldData } from '../../services/fieldDataService';

interface Field {
  id: string;
  farm_id: string;
  farm_name: string;
  name: string;
  size: number | null;
  size_unit: string;
  field_type: string;
  crop_type: string;
  status: string;
  location: string;
  boundaries: string;
  soil_type: string;
  last_soil_test_date: string;
  notes: string;
  created_at: string;
  updated_at: string;
}

const FieldDetail = () => {
  const { fieldId } = useParams<{ fieldId: string }>();
  const navigate = useNavigate();
  const [field, setField] = useState<Field | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);

  // Data.gov field information
  const [fieldDataGov, setFieldDataGov] = useState<FieldDataGov | null>(null);
  const [recommendedCrops, setRecommendedCrops] = useState<string[]>([]);
  const [conservationPractices, setConservationPractices] = useState<string[]>([]);
  const [historicalYieldData, setHistoricalYieldData] = useState<FieldDataGov['historical_yield_data']>([]);
  const [dataGovLoading, setDataGovLoading] = useState(true);
  const [dataGovError, setDataGovError] = useState<string | null>(null);


  // Fetch field data
  useEffect(() => {
    const fetchField = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`${API_URL}/fields/${fieldId}`);
        setField(response.data);
      } catch (err: any) {
        console.error('Error fetching field:', err);
        setError('Failed to load field data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (fieldId) {
      fetchField();
    }
  }, [fieldId]);

  // Fetch data.gov field information
  useEffect(() => {
    const fetchDataGovInfo = async () => {
      if (!fieldId) return;

      setDataGovLoading(true);
      setDataGovError(null);

      try {
        // Fetch all field data in a single request
        const allFieldData = await getAllFieldData(fieldId);

        setFieldDataGov(allFieldData.fieldData);
        setRecommendedCrops(allFieldData.recommendedCrops);
        setConservationPractices(allFieldData.conservationPractices);
        setHistoricalYieldData(allFieldData.historicalYieldData);
      } catch (err: any) {
        console.error('Error fetching field data:', err);
        setDataGovError('Failed to load field data. Please try again later.');

        // Fallback to separate requests if the consolidated request fails
        try {
          // Fetch all data.gov field information in parallel
          const [fieldDataResponse, recommendedCropsResponse, conservationPracticesResponse, historicalYieldDataResponse] = await Promise.all([
            getFieldDataGov(fieldId),
            getFieldRecommendedCrops(fieldId),
            getFieldConservationPractices(fieldId),
            getFieldHistoricalYieldData(fieldId)
          ]);

          setFieldDataGov(fieldDataResponse);
          setRecommendedCrops(recommendedCropsResponse);
          setConservationPractices(conservationPracticesResponse);
          setHistoricalYieldData(historicalYieldDataResponse);
        } catch (fallbackErr: any) {
          console.error('Error fetching field data with fallback method:', fallbackErr);
          setDataGovError('Failed to load field data. Please try again later.');
        }
      } finally {
        setDataGovLoading(false);
      }
    };

    if (fieldId && !loading && field) {
      fetchDataGovInfo();
    }
  }, [fieldId, loading, field]);

  // Handle delete
  const handleDelete = async () => {
    setLoading(true);
    setError(null);

    try {
      await axios.delete(`${API_URL}/fields/${fieldId}`);
      navigate('/fields');
    } catch (err: any) {
      console.error('Error deleting field:', err);
      setError('Failed to delete field. Please try again later.');
      setLoading(false);
    }
  };

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  // Format field type for display
  const formatFieldType = (type: string) => {
    if (!type) return 'N/A';
    return type.charAt(0).toUpperCase() + type.slice(1).replace('_', ' ');
  };

  // Format status for display with badge
  const getStatusBadge = (status: string) => {
    let bgColor = 'bg-gray-100 text-gray-800';

    switch (status) {
      case 'active':
        bgColor = 'bg-green-100 text-green-800';
        break;
      case 'fallow':
        bgColor = 'bg-yellow-100 text-yellow-800';
        break;
      case 'planted':
        bgColor = 'bg-blue-100 text-blue-800';
        break;
      case 'harvested':
        bgColor = 'bg-purple-100 text-purple-800';
        break;
      case 'inactive':
        bgColor = 'bg-gray-100 text-gray-800';
        break;
    }

    return (
      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${bgColor}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        </div>
      </Layout>
    );
  }

  if (error || !field) {
    return (
      <Layout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error || 'Field not found'}</span>
        </div>
        <Link
          to="/fields"
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Fields
        </Link>
      </Layout>
    );
  }

  return (
    <Layout>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">{field.name}</h1>
        <div className="flex space-x-2">
          <Link
            to="/fields"
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Fields
          </Link>
          <Link
            to={`/fields/${fieldId}/edit`}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Edit Field
          </Link>
          <button
            onClick={() => setDeleteModalOpen(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Delete
          </button>
        </div>
      </div>

      {/* Field Details */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <div>
            <h3 className="text-lg leading-6 font-medium text-gray-900">Field Details</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">Details and information about the field.</p>
          </div>
          <div>
            {getStatusBadge(field.status)}
          </div>
        </div>
        <div className="border-t border-gray-200">
          <dl>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Farm</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{field.farm_name}</dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Field Type</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{formatFieldType(field.field_type)}</dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Crop Type</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{formatFieldType(field.crop_type)}</dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Size</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {field.size ? `${field.size} ${field.size_unit}` : 'N/A'}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Location</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{field.location || 'N/A'}</dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Soil Type</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {field.soil_type ? formatFieldType(field.soil_type) : 'N/A'}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Last Soil Test</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {formatDate(field.last_soil_test_date)}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Field Boundaries</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {field.boundaries || 'No boundary data available'}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Notes</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 whitespace-pre-line">
                {field.notes || 'No notes available'}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Created</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{formatDate(field.created_at)}</dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{formatDate(field.updated_at)}</dd>
            </div>
          </dl>
        </div>
      </div>

      {/* Data.gov Field Information */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Data.gov Field Information</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">Field information from USDA data.gov APIs.</p>
        </div>

        {dataGovLoading ? (
          <div className="px-4 py-5 sm:px-6 flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
          </div>
        ) : dataGovError ? (
          <div className="px-4 py-5 sm:px-6">
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error loading data.gov field information</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{dataGovError}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : fieldDataGov ? (
          <div className="border-t border-gray-200">
            <dl>
              <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Soil Type</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{fieldDataGov.soil_type}</dd>
              </div>
              <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Soil Health Index</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  <div className="flex items-center">
                    <div className="w-full bg-gray-200 rounded-full h-2.5">
                      <div className="bg-green-600 h-2.5 rounded-full" style={{ width: `${fieldDataGov.soil_health_index}%` }}></div>
                    </div>
                    <span className="ml-2">{fieldDataGov.soil_health_index}%</span>
                  </div>
                </dd>
              </div>
              <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Land Capability Class</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{fieldDataGov.land_capability_class}</dd>
              </div>
              <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Erosion Risk</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    fieldDataGov.erosion_risk === 'Low' ? 'bg-green-100 text-green-800' :
                    fieldDataGov.erosion_risk === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {fieldDataGov.erosion_risk}
                  </span>
                </dd>
              </div>
              <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Water Availability</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    fieldDataGov.water_availability === 'High' ? 'bg-green-100 text-green-800' :
                    fieldDataGov.water_availability === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {fieldDataGov.water_availability}
                  </span>
                </dd>
              </div>
              <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{formatDate(fieldDataGov.last_updated)}</dd>
              </div>
            </dl>
          </div>
        ) : (
          <div className="px-4 py-5 sm:px-6">
            <p className="text-sm text-gray-500">No data.gov field information available.</p>
          </div>
        )}
      </div>

      {/* Recommended Crops */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Recommended Crops</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">Crops recommended for this field based on soil and climate data.</p>
        </div>

        {dataGovLoading ? (
          <div className="px-4 py-5 sm:px-6 flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
          </div>
        ) : recommendedCrops.length > 0 ? (
          <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
            <div className="flex flex-wrap gap-2">
              {recommendedCrops.map((crop, index) => (
                <span key={index} className="px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                  {crop}
                </span>
              ))}
            </div>
          </div>
        ) : (
          <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
            <p className="text-sm text-gray-500">No recommended crops available.</p>
          </div>
        )}
      </div>

      {/* Conservation Practices */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Conservation Practices</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">Recommended conservation practices for this field.</p>
        </div>

        {dataGovLoading ? (
          <div className="px-4 py-5 sm:px-6 flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
          </div>
        ) : conservationPractices.length > 0 ? (
          <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
            <ul className="list-disc pl-5 space-y-2">
              {conservationPractices.map((practice, index) => (
                <li key={index} className="text-sm text-gray-900">{practice}</li>
              ))}
            </ul>
          </div>
        ) : (
          <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
            <p className="text-sm text-gray-500">No conservation practices available.</p>
          </div>
        )}
      </div>

      {/* Historical Yield Data */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Historical Yield Data</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">Past yield data for this field.</p>
        </div>

        {dataGovLoading ? (
          <div className="px-4 py-5 sm:px-6 flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
          </div>
        ) : historicalYieldData.length > 0 ? (
          <div className="border-t border-gray-200">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Year</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Crop</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Yield</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {historicalYieldData.map((data, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{data.year}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{data.crop}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{data.yield} {data.unit}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ) : (
          <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
            <p className="text-sm text-gray-500">No historical yield data available.</p>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {deleteModalOpen && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-y-auto max-h-[90vh] shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg className="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                      Delete Field
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Are you sure you want to delete this field? This action cannot be undone.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={handleDelete}
                >
                  Delete
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => setDeleteModalOpen(false)}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default FieldDetail;
