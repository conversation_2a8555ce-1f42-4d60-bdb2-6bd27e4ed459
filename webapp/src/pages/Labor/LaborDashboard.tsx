import { useContext } from 'react';
import { Link } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';

const LaborDashboard = () => {
  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  // Dashboard cards for different labor management features
  const dashboardCards = [
    {
      title: 'Seasonal Workers',
      description: 'Manage seasonal workers, including documentation and compliance.',
      icon: '👨‍🌾',
      link: '/labor/seasonal-workers',
      color: 'bg-blue-100',
      textColor: 'text-blue-800',
      borderColor: 'border-blue-200'
    },
    {
      title: 'Labor Cost Analysis',
      description: 'Analyze labor costs by task, crop, and field.',
      icon: '💰',
      link: '/labor/cost-analysis',
      color: 'bg-green-100',
      textColor: 'text-green-800',
      borderColor: 'border-green-200'
    },
    {
      title: 'Compliance Tracking',
      description: 'Ensure compliance with agricultural labor regulations.',
      icon: '📋',
      link: '/labor/compliance',
      color: 'bg-yellow-100',
      textColor: 'text-yellow-800',
      borderColor: 'border-yellow-200'
    },
    {
      title: 'Worker Certifications',
      description: 'Track worker certifications and training.',
      icon: '🎓',
      link: '/labor/certifications',
      color: 'bg-purple-100',
      textColor: 'text-purple-800',
      borderColor: 'border-purple-200'
    }
  ];

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Labor Management</h1>
      </div>

      <div className="bg-white shadow rounded-lg p-6 mb-8">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Labor Management Overview</h2>
        <p className="text-gray-600 mb-4">
          Manage your farm's labor resources efficiently, including seasonal workers, labor costs, 
          compliance with agricultural labor regulations, and worker certifications.
          Track labor costs by task, crop, and field to optimize your farm's operations.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
        {dashboardCards.map((card, index) => (
          <Link 
            key={index} 
            to={card.link} 
            className={`block p-6 rounded-lg shadow-sm border ${card.borderColor} ${card.color} hover:shadow-md transition-shadow duration-200`}
          >
            <div className="flex items-center mb-3">
              <span className="text-3xl mr-3">{card.icon}</span>
              <h3 className={`text-lg font-medium ${card.textColor}`}>{card.title}</h3>
            </div>
            <p className="text-gray-600">{card.description}</p>
          </Link>
        ))}
      </div>

      <div className="mt-8 bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link 
            to="/labor/seasonal-workers/new" 
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Add Seasonal Worker
          </Link>
          <Link 
            to="/labor/cost-analysis/new" 
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            Create Cost Analysis
          </Link>
          <Link 
            to="/labor/compliance/new" 
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
          >
            Add Compliance Record
          </Link>
          <Link 
            to="/labor/certifications/new" 
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            Add Worker Certification
          </Link>
        </div>
      </div>
    </Layout>
  );
};

export default LaborDashboard;