import axios, { AxiosError } from 'axios';
import { NavigateFunction } from 'react-router-dom';

/**
 * Error response interface from the API
 */
export interface ApiErrorResponse {
  message: string;
  errorType?: string;
  errorCode?: string;
  context?: any;
  error?: {
    stack?: string;
  };
}

/**
 * Structured error object with detailed information
 */
export interface StructuredError {
  message: string;
  type: string;
  code: string;
  context?: any;
  originalError?: unknown;
}

/**
 * Handles API errors and returns a structured error object
 * @param error The error object from the API call
 * @param defaultErrorMessage The default error message to display if none is provided
 * @returns A structured error object with detailed information
 */
export const handleApiError = (
  error: unknown, 
  navigate?: NavigateFunction,
  defaultErrorMessage = 'An unexpected error occurred'
): StructuredError => {
  // Default structured error
  const structuredError: StructuredError = {
    message: defaultErrorMessage,
    type: 'UnknownError',
    code: 'UNKNOWN',
    originalError: error
  };

  if (error instanceof Error) {
    structuredError.message = error.message;
    structuredError.type = error.name;
  }

  // Handle Axios errors
  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError<ApiErrorResponse>;
    const status = axiosError.response?.status;
    const data = axiosError.response?.data;

    // Set status code
    structuredError.code = status?.toString() || 'NETWORK_ERROR';

    // Extract detailed error information from response
    if (data) {
      structuredError.message = data.message || structuredError.message;
      structuredError.type = data.errorType || `HTTP${status}Error`;
      structuredError.code = data.errorCode || structuredError.code;
      structuredError.context = data.context;
    }

    // Handle navigation if navigate function is provided
    if (navigate) {
      // Only redirect for specific status codes
      switch (status) {
        case 401:
          // Unauthorized - redirect to login page
          navigate('/login');
          break;
        case 403:
          // Forbidden - redirect to forbidden page
          navigate('/forbidden');
          break;
      }
    }
  }

  return structuredError;
};

/**
 * Checks if the server is in maintenance mode
 * @returns A promise that resolves to true if the server is in maintenance mode
 */
export const checkMaintenanceMode = async (): Promise<boolean> => {
  try {
    // Make a request to the server to check if it's in maintenance mode
    const response = await fetch('/api/status');
    const data = await response.json();

    return data.maintenance === true;
  } catch (error) {
    // If there's an error, assume the server is not in maintenance mode
    return false;
  }
};

/**
 * Redirects to the maintenance page if the server is in maintenance mode
 * @param navigate The navigate function from useNavigate
 */
export const redirectIfMaintenance = async (navigate: NavigateFunction): Promise<void> => {
  const isMaintenanceMode = await checkMaintenanceMode();

  if (isMaintenanceMode) {
    navigate('/maintenance');
  }
};
